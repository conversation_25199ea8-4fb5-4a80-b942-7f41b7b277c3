import * as React from "react";
import * as TabsPrimitive from "@radix-ui/react-tabs"; // นำเข้า primitive components ทั้งหมดจาก Radix Tabs library
import { cn } from "../../../../utils/utils"; // นำเข้า utility function สำหรับจัดการ class names

/**
 * Root container component for the tab system.
 *
 * Wraps the entire Tabs interface including triggers and content areas.
 * Acts as the state manager for active tab selection.
 *
 * This is a direct re-export of `TabsPrimitive.Root` from `@radix-ui/react-tabs`.
 *
 * @component
 * @see https://www.radix-ui.com/docs/primitives/components/tabs
 */
const Tabs = TabsPrimitive.Root;

/**
 * TabsList is the wrapper around the tab triggers (buttons).
 *
 * It lays out the tab buttons horizontally and provides styling for the group.
 *
 * @component
 * @param {Object} props - Props passed to the component
 * @param {string} [props.className] - Additional class names for styling
 * @param {React.Ref} ref - Ref forwarded to the underlying DOM element
 * @returns {JSX.Element}
 *
 * @example
 * <TabsList className="custom-class">
 *   <TabsTrigger value="tab1">Tab 1</TabsTrigger>
 *   <TabsTrigger value="tab2">Tab 2</TabsTrigger>
 * </TabsList>
 */
const TabsList = React.forwardRef(({ className, ...props }, ref) => (
  <TabsPrimitive.List
    ref={ref}
    className={cn(
      "inline-flex h-18 items-center justify-center rounded-md bg-muted p-2 text-muted-foreground bg-gray-200 ",
      className
    )}
    {...props}
  />
));
TabsList.displayName = TabsPrimitive.List.displayName;

/**
 * TabsTrigger represents an individual tab button.
 *
 * When clicked, it activates its corresponding `<TabsContent>` by matching `value`.
 * Includes styling for active state and disabled state.
 *
 * @component
 * @param {Object} props - Props passed to the component
 * @param {string} props.value - The value identifier that matches the `<TabsContent>`
 * @param {string} [props.className] - Additional class names for styling
 * @param {React.Ref} ref - Ref forwarded to the underlying DOM element
 * @returns {JSX.Element}
 *
 * @example
 * <TabsTrigger value="settings">Settings</TabsTrigger>
 */
const TabsTrigger = React.forwardRef(({ className, ...props }, ref) => (
  <TabsPrimitive.Trigger
    ref={ref}
    className={cn(
      "inline-flex items-center justify-center whitespace-nowrap rounded-sm px-6 py-4 text-lg font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-white data-[state=active]:text-black data-[state=active]:shadow-sm",
      className
    )}
    {...props}
  />
));
TabsTrigger.displayName = TabsPrimitive.Trigger.displayName;

/**
 * TabsContent displays the content for the currently active tab.
 *
 * Each `<TabsContent>` must have a `value` that matches its corresponding `<TabsTrigger>`.
 * Hidden when inactive. Visible when `value` matches current selected tab.
 *
 * @component
 * @param {Object} props - Props passed to the component
 * @param {string} props.value - The matching value for this content
 * @param {string} [props.className] - Additional class names for styling
 * @param {React.Ref} ref - Ref forwarded to the underlying DOM element
 * @returns {JSX.Element}
 *
 * @example
 * <TabsContent value="account">
 *   <p>Account Settings here</p>
 * </TabsContent>
 */
const TabsContent = React.forwardRef(({ className, ...props }, ref) => (
  <TabsPrimitive.Content
    ref={ref}
    className={cn(
      "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
      className
    )}
    {...props}
  />
));
TabsContent.displayName = TabsPrimitive.Content.displayName;

export { Tabs, TabsList, TabsTrigger, TabsContent };
