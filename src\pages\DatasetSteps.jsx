import React from "react";
import { motion } from "framer-motion";
import DatasetManagement from "../components/User/Dataset/Upload/DatasetManagement";
import DataCleansing from "../components/User/Dataset/Cleansing/DataCleansing";
import DataVisualization from "../components/User/Dataset/Visualization/DataVisualization";
import StatisticalAnalysis from "../components/User/Dataset/Statistic/StatisticalAnalysis";
import ModelPrediction from "../components/User/Dataset/Predict/ModelPrediction";

import { useSelector, useDispatch } from "react-redux";
import {
  setNextStep,
  setPrevStep,
} from "../app/features/progress/progressSlice";

/**
 * `ProgressSteps` is a multi-step progress interface for guiding users through
 * a machine learning workflow: uploading data, cleansing, visualizing, analyzing, and modeling.
 *
 * It uses Redux to manage the current step and Framer Motion to animate transitions.
 *
 * @component
 * @returns {JSX.Element} A step-based UI with progress indicators and navigation buttons.
 */
const ProgressSteps = () => {
  /**
 * Retrieves the list of steps from Redux store.
 * Each step should contain a `title` property.
 *
 * @type {Array<{ title: string }>}
 */
  const steps = useSelector((state) => state.progress.steps); // Default to an empty array
  /**
 * Retrieves the current active step index from Redux store.
 *
 * @type {number}
 */
  const currentStep = useSelector((state) => state.progress.currentStep); // Default to 0
  /**
 * Redux dispatch function used to trigger actions.
 *
 * @type {Function}
 */
  const dispatch = useDispatch();
  /**
   * Dispatch action to advance to the next step.
   * Disabled if already at the last step.
   */
  const nextStep = () => {
    dispatch(setNextStep());
  };

  /**
   * Dispatch action to go back to the previous step.
   * Disabled if already at the first step.
   */
  const prevStep = () => {
    dispatch(setPrevStep());
  };

  /**
 * Render the content associated with the current step.
 * @returns {JSX.Element} The component representing the current workflow step.
 */
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return <DatasetManagement />;
      case 1:
        return <DataCleansing />;
      case 2:
        return <DataVisualization />;
      case 3:
        return <StatisticalAnalysis />;
      case 4:
        return <ModelPrediction />;
      default:
        return <div>Invalid Step</div>;
    }
  };

  return (
    <div className="flex flex-col items-center p-5">
      {/* Progress Bar */}
      <div className="relative flex justify-between w-[40rem] mb-5">
        {/* Animated progress line */}
        <motion.div
          className="absolute h-1 bg-blue-500 mt-[1rem]"
          style={{
            width: `${((currentStep + 1) / steps.length) * 100}%`,
            transition: "width 0.3s ease",
          }}
        />
        {/* Step indicators */}
        {steps.map((step, index) => (
          <div key={index} className="flex flex-col items-center relative">
            <motion.div
              className={`h-10 w-10 flex justify-center items-center rounded-full border-2 ${index <= currentStep ? "bg-blue-500" : "bg-gray-200"
                }`}
              whileHover={{ scale: 1.1 }}
              transition={{ duration: 0.2 }}
            >
              {index < currentStep ? "✅" : null}
            </motion.div>
            <p
              className={`mt-2 ${index <= currentStep ? "text-blue-500" : "text-gray-500"
                }`}
            >
              {step.title}
            </p>
          </div>
        ))}
      </div>

      {/* Dynamic step content */}
      <motion.div
        className="p-5 border rounded-lg shadow-md w-full"
        key={currentStep}
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
      >
        {renderStepContent()}
      </motion.div>

      {/* Navigation Buttons */}
      <div className="flex mt-5 space-x-4">
        <button
          onClick={prevStep}
          className={`px-4 py-2 text-white bg-blue-500 rounded ${currentStep === 0 ? "opacity-50 cursor-not-allowed" : ""
            }`}
          disabled={currentStep === 0}
        >
          Previous
        </button>
        <button
          onClick={nextStep}
          className={`px-4 py-2 text-white bg-blue-500 rounded ${currentStep === steps.length - 1
            ? "opacity-50 cursor-not-allowed"
            : ""
            }`}
          disabled={currentStep === steps.length - 1}
        >
          Next
        </button>
      </div>
    </div>
  );
};

export default ProgressSteps;
