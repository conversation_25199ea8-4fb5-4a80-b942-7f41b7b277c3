// components/DimensionalReductionPlot.js
import React from "react";
import Plot from "react-plotly.js";

const DimensionalReductionPlot = ({ data2D, data3D }) => {
    return (
        <div className="w-full flex flex-col gap-10">
            {/* 2D Plot */}
            <div className="border p-3 rounded shadow-md">
                <h2 className="text-lg font-semibold mb-2">2D Dimensional Reduction</h2>
                <Plot
                    data={[
                        {
                            x: data2D.map((d) => d[0]),
                            y: data2D.map((d) => d[1]),
                            mode: "markers",
                            type: "scatter",
                            marker: { color: "blue", size: 8 },
                        },
                    ]}
                    layout={{ width: 600, height: 400, title: "2D Plot" }}
                />
            </div>

            {/* 3D Plot */}
            <div className="border p-3 rounded shadow-md">
                <h2 className="text-lg font-semibold mb-2">3D Dimensional Reduction</h2>
                <Plot
                    data={[
                        {
                            x: data3D.map((d) => d[0]),
                            y: data3D.map((d) => d[1]),
                            z: data3D.map((d) => d[2]),
                            mode: "markers",
                            type: "scatter3d",
                            marker: { color: "red", size: 4 },
                        },
                    ]}
                    layout={{ width: 600, height: 500, title: "3D Plot" }}
                />
            </div>
        </div>
    );
};

export default DimensionalReductionPlot;
