import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  features: [], // <-- array of selected feature for handle outlier method
  selectedOutlierOption: "", // <-- array of ["Trimming", "Capping", "Power Transformation"]
  selectedTrimmingOption: "", // <-- array of ["Z-score", "IQR", "Percentile"]
  selectedCappingOption: "", // <-- array of ["Z-score", "IQR", "Percentile"]
  zScore: 3, // <-- Threshold value of z-score slider
  iqr: 1.5, // <-- Threshold value of IQR slider
  percentile: { lower: 1, upper: 99 }, // <-- Upper and Lower value of percentile
  powerTransformation: { transformation: "", lambda: 1 }, // <-- { transformation: "Box-Cox", lambda: 1 }
  showLampda: false,
};

export const outlierSlice = createSlice({
  name: "outlier",
  initialState,
  reducers: {
    setOutlier: (state, action) => {
      return { ...state, ...action.payload };
    },
    resetOutlier: () => {
      return initialState;
    },
  },
});

export const { setOutlier, resetOutlier } = outlierSlice.actions;
export default outlierSlice.reducer;
