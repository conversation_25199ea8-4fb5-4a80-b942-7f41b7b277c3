import React, { useState, useEffect } from "react";

const RidgeRegressionForm = ({ onParamsChange }) => {
    const [params, setParams] = useState({
        alpha: 1,
        solver: "auto",
        fit_intercept: "true",
    });

    useEffect(() => {
        onParamsChange(params);
    }, [params, onParamsChange]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setParams((prevParams) => ({
            ...prevParams,
            [name]: value,
        }));
    };

    return (
        <div className="space-y-2 p-4 bg-white rounded shadow">

            <div>
                <label className="block text-sm font-medium">alpha:</label>
                <input
                    type="number"
                    name="alpha"
                    value={params.alpha}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                    step="10"
                />
            </div>

            <div>
                <label className="block text-sm font-medium">solver:</label>
                <select
                    name="solver"
                    value={params.solver}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                >
                    <option value="auto">auto</option>
                    <option value="svd">svd</option>
                    <option value="cholesky">cholesky</option>
                    <option value="sparse_cg">sparse_cg</option>
                    <option value="sag">sag</option>
                    <option value="saga">saga</option>
                    <option value="lbfgs">lbfgs</option>
                </select>
            </div>

            <div>
                <label className="block text-sm font-medium">fit_intercept:</label>
                <select
                    name="fit_intercept"
                    value={params.fit_intercept}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                >
                    <option value="true">True</option>
                    <option value="no">Not Selected</option>
                </select>
            </div>
        </div>
    );
};

export default RidgeRegressionForm;
