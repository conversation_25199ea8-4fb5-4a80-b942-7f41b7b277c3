import React, { useState, useEffect, useMemo } from "react";
import { useSelector } from "react-redux";

/**
 * Component for managing and correcting typos in dataset columns
 */
const CleansingTypos = ({ resetParameter, setResetParameter }) => {
  /**
   * Redux state containing the dataset and feature
   * @type {{dataset: [{row1},{row2}]}}
   * @type {{features: {allFeatureOrder:[], unassigned:{}, numerical:{}, nominal:{}, ordinal:{}}}}
   */
  const { dataset, features: reduxFeatures } = useSelector(
    (state) => state.upload
  );

  /**
   * State for storing available columns that match the features
   * @type {[string[], Function]}
   */
  const [columns, setColumns] = useState([]);

  /**
   * State for the currently selected column
   * @type {[string, Function]}
   */
  const [selectedColumn, setSelectedColumn] = useState("");

  /**
   * State for storing unique values in the selected column
   * @type {[Array, Function]}
   */
  const [uniqueValues, setUniqueValues] = useState([]);

  /**
   * State for storing corrections mapping original values to corrected values
   * @type {[Object, Function]}
   */
  const [corrections, setCorrections] = useState({});

  /**
   * Variable for collect categorical feature
   */
  const features = useMemo(
    () => [...reduxFeatures.nominal.items, ...reduxFeatures.ordinal.items],
    [reduxFeatures.nominal.items, reduxFeatures.ordinal.items]
  );

  /**
   * Effect hook that initializes and updates available columns based on dataset and features
   * @effect
   * set available features select based on the nominal and ordinal features
   *
   * @description
   * This effect runs whenever the dataset or features change. It:
   * 1. Checks if the dataset has any rows
   * 2. Extracts all column names from the first row of the dataset
   * 3. Filters the columns to only categorical features
   * 4. Updates the columns state with the categorical feature
   *
   * The effect ensures that only columns that are part of the categorical features
   * are available for typo correction, preventing users from selecting
   * irrelevant or non-feature columns.
   *
   * @dependencies
   * - dataset: Array of data objects containing the dataset rows
   * - features: Array of feature objects containing column information
   */
  useEffect(() => {
    if (dataset.length > 0) {
      const allColumns = Object.keys(dataset[0]);
      const filteredColumns = allColumns.filter((col) =>
        features.some((feature) => feature.feature === col)
      );
      setColumns(filteredColumns);
    }
  }, [dataset, features]);

  /**
   * Handles feature selection
   * @param {string} column - The name of the selected feature
   * @returns {void}
   *
   * @description
   * This function is triggered when a user selects a feature from the dropdown.
   * It updates the selectedColumn state
   *
   *
   * @example
   * // When user selects "name" column
   * handleColumnSelect("name")
   */
  const handleColumnSelect = (column) => {
    setSelectedColumn(column);
    setResetParameter(false);
  };

  /**
   * @effect
   * check if selectedColumn are truthy value or not. if so,:
   * set unique values based on the selected column
   * set corrections based on the unique values
   *
   * If selectedColumn are falsy value:
   * - Clears unique values array
   * - Resets corrections object
   *
   * @dependencies
   * - selectedColumn: state containing current selected column
   */
  useEffect(() => {
    if (selectedColumn) {
      const values = dataset.map((item) => item[selectedColumn]);
      const unique = [...new Set(values)];
      setUniqueValues(unique);
      const newCorrections = Object.fromEntries(
        unique.map((val) => [val, val])
      );
      setCorrections(newCorrections);
    } else {
      setUniqueValues([]);
      setCorrections({});
    }
  }, [selectedColumn]);

  /**
   * Handles updating individual typo corrections in 'corrections' state
   * @param {string} value - The new corrected value entered by the user
   * @param {string} original - The original value that is from dataset
   * @returns {void}
   *
   * @description
   * This function is triggered when a user modifies a correction in the input field.
   * It performs the following operations:
   * 1. Creates a new corrections object with the updated value
   * 2. Updates the 'corrections' state with the new corrections
   *
   * @example
   * // When user corrects "Jon" to "John"
   * handleCorrectionChange("John", "Jon")
   */
  const handleCorrectionChange = (value, original) => {
    const newCorrections = {
      ...corrections,
      [original]: value,
    };
    setCorrections(newCorrections);
  };

  // useEffect for reset parameter when click reset parameter button
  useEffect(() => {
    if (resetParameter) {
      setSelectedColumn("");
      setUniqueValues([]);
      setCorrections({});
    }
  }, [resetParameter]);

  useEffect(() => {
    console.log("selectedColumn: ", selectedColumn);
  }, [selectedColumn]);

  return (
    <div className="max-w-4xl mx-auto my-8 p-6 bg-white rounded-xl shadow-md space-y-6">
      <h2 className="text-2xl font-bold">Change manually</h2>

      <div>
        <label className="block font-medium mb-2">Select Column:</label>
        <select
          className="border border-gray-300 p-2 rounded w-full"
          value={selectedColumn}
          onChange={(e) => handleColumnSelect(e.target.value)}
        >
          <option value="">-- Choose Column --</option>
          {columns.map((col) => (
            <option key={col} value={col}>
              {col}
            </option>
          ))}
        </select>
      </div>

      {selectedColumn && (
        <div>
          <h3 className="text-lg font-semibold mb-4">Correct Typo</h3>
          <div className="space-y-3">
            {uniqueValues.map((value) => (
              <div key={value} className="flex gap-4 items-center mb-2">
                <span className="w-1/3 truncate">{value}</span>
                <input
                  type="text"
                  className="border p-2 rounded w-2/3"
                  value={corrections[value]}
                  onChange={(e) =>
                    handleCorrectionChange(e.target.value, value)
                  }
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CleansingTypos;
