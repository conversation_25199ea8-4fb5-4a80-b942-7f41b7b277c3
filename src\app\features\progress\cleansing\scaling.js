import { createSlice } from "@reduxjs/toolkit";

/**
 * Initial state for feature scaling configuration.
 * 
 * @typedef {Object} ScalingState
 * @property {string|null} method - The selected scaling method (e.g., "min-max", "standard", or null if none selected).
 * @property {Array<string>} features - List of feature names or IDs selected for scaling.
 */
const initialState = {
    method: null,
    features: [],  // เพิ่มเก็บ features ที่เลือก
};

/**
 * Redux slice to manage scaling configuration for dataset features.
 * Allows setting and resetting scaling method and the features to apply scaling.
 * 
 * @module scalingSlice
 */
const scalingSlice = createSlice({
    name: "scaling",
    initialState,
    reducers: {
        /**
         * Updates scaling state with the given method and/or features.
         * Only properties provided in the action payload will be updated.
         * 
         * @param {ScalingState} state - Current scaling slice state.
         * @param {Object} action - Redux action object.
         * @param {Object} action.payload - Payload containing new values to set.
         * @param {string} [action.payload.method] - Scaling method to set.
         * @param {Array<string>} [action.payload.features] - Features list to set.
         * 
         * @example
         * dispatch(setScaling({ method: "min-max", features: ["age", "income"] }));
         */
        setScaling: (state, action) => {
            const { method, features } = action.payload;
            if (method !== undefined) {
                state.method = method;
            }
            if (features !== undefined) {
                state.features = features;
            }
        },

        /**
         * Resets the scaling state to the initial default values.
         * 
         * @returns {ScalingState} The initial scaling state.
         * 
         * @example
         * dispatch(resetScaling());
         */
        resetScaling: () => {
            return initialState;
        },
    },
});

export const { setScaling, resetScaling } = scalingSlice.actions;
export default scalingSlice.reducer;
