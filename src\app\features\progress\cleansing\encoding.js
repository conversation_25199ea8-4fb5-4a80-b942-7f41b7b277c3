import { createSlice } from "@reduxjs/toolkit";

/**
 * Initial state for encoding feature configuration.
 *
 * @typedef {Object} EncodingState
 * @property {string} nominalMethod - The encoding method selected for nominal features (e.g., "one-hot", "label").
 * @property {string} ordinalMethod - The encoding method selected for ordinal features (e.g., "label", "ordinal").
 * @property {Array<string>} selectedOrdinalFeatures - List of feature names or IDs selected as ordinal.
 * @property {Array<string>} selectedNominalFeatures - List of feature names or IDs selected as nominal.
 * @property {Object<string, string>} dummyPrefix - Object mapping feature IDs to their dummy variable prefixes for one-hot encoding.
 * @property {Object<string, Array<{category: string, code: number, count: number}>>} nominalMappings - Object mapping nominal feature IDs to their category-to-code mappings.
 * @property {Object<string, Array<{category: string, code: number, count: number}>>} ordinalMappings - Object mapping ordinal feature IDs to their category-to-code mappings.
 */
const initialState = {
  nominalMethod: "",
  ordinalMethod: "",
  selectedOrdinalFeatures: [],
  selectedNominalFeatures: [],
  dummyPrefix: {}, // เปลี่ยนเป็น object เก็บ prefix ของแต่ละ feature
  nominalMappings: {}, // เก็บ mapping ของ nominal features
  ordinalMappings: {}, // เก็บ mapping ของ ordinal features
};

/**
 * Redux slice for managing encoding configuration of features.
 * Supports setting encoding methods, selected features, and dummy variable prefixes.
 *
 * @module encodingSlice
 */
const encodingSlice = createSlice({
  name: "encoding",
  initialState,
  reducers: {
    /**
     * Sets encoding state partially or fully by merging the payload into current state.
     *
     * @param {Partial<EncodingState>} action.payload - Partial encoding state to merge.
     * @example
     * dispatch(setEncoding({ nominalMethod: "one-hot", ordinalMethod: "label" }));
     */
    setEncoding(state, action) {
      return { ...state, ...action.payload };
    },

    /**
     * Updates the list of selected ordinal features.
     *
     * @param {Array<string>} action.payload - Array of feature IDs/names to set as ordinal.
     * @example
     * dispatch(setSelectedOrdinalFeatures(["feature1", "feature2"]));
     */
    setSelectedOrdinalFeatures(state, action) {
      state.selectedOrdinalFeatures = action.payload;
    },

    /**
     * Updates the list of selected nominal features.
     *
     * @param {Array<string>} action.payload - Array of feature IDs/names to set as nominal.
     * @example
     * dispatch(setSelectedNominalFeatures(["feature3", "feature4"]));
     */
    setSelectedNominalFeatures(state, action) {
      state.selectedNominalFeatures = action.payload;
    },

    /**
     * Updates the dummy prefix mapping object by merging new prefixes for features.
     *
     * @param {Object<string, string>} action.payload - Object mapping feature IDs to dummy prefixes.
     * @example
     * dispatch(setDummyPrefix({ feature1: "prefix1", feature2: "prefix2" }));
     */
    setDummyPrefix(state, action) {
      // ตัวอย่าง payload: { featureId: "prefix" }
      state.dummyPrefix = {
        ...state.dummyPrefix,
        ...action.payload,
      };
    },

    /**
     * Updates the numeric code mapping for a single nominal feature.
     *
     * @param {Object} action.payload - Object with feature and mappings.
     * @param {string} action.payload.feature - The feature to update.
     * @param {Array<{category: string, code: number, count: number}>} action.payload.mappings - The category mappings for this feature.
     * @example
     * dispatch(setNominalFeatureMapping({ feature: "feature1", mappings: [{category: "A", code: 0, count: 10}] }));
     */
    setNominalFeatureMapping(state, action) {
      const { featureId, mappings } = action.payload;
      state.nominalMappings[featureId] = mappings;
    },

    /**
     * Updates the numeric code mapping for a single ordinal feature.
     *
     * @param {Object} action.payload - Object with feature and mappings.
     * @param {string} action.payload.feature - The feature ID to update.
     * @param {Array<{category: string, code: number, count: number}>} action.payload.mappings - The category mappings for this feature.
     * @example
     * dispatch(setOrdinalFeatureMapping({ feature: "feature2", mappings: [{category: "Low", code: 0, count: 5}] }));
     */
    setOrdinalFeatureMapping(state, action) {
      const { featureId, mappings } = action.payload;
      state.ordinalMappings[featureId] = mappings;
    },

    /**
     * Resets encoding state to the initial default values.
     *
     * @returns {EncodingState} Initial encoding state.
     * @example
     * dispatch(resetEncoding());
     */
    resetEncoding: () => initialState,
  },
});

export const {
  setEncoding,
  setSelectedOrdinalFeatures,
  setSelectedNominalFeatures,
  resetEncoding,
  setDummyPrefix,
  setNominalFeatureMapping,
  setOrdinalFeatureMapping,
} = encodingSlice.actions;

export default encodingSlice.reducer;
