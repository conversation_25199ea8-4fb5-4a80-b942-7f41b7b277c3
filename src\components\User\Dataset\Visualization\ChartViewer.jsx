import React, { useEffect, useRef } from "react"; // ใช้อ้างอิง DOM element เช่น <canvas> หรือ <div> สำหรับ Chart.js และ Plotly
import Chart from "chart.js/auto"; // ไลบรารีสำหรับสร้างกราฟพื้นฐาน เช่น bar, line, pie charts
import ChartDataLabels from "chartjs-plugin-datalabels"; // plugin สำหรับแสดง labels บน chart.js
import Plotly from "plotly.js-dist-min"; // ไลบรารีสร้างกราฟขั้นสูง เช่น scatter และ pie charts

/**
 * ChartViewer is a dynamic React component that visualizes a given dataset
 * using various chart types such as bar, line, pie, scatter, and more.
 * It supports grouping by categorical features and renders charts using Chart.js or Plotly.js.
 *
 * Chart Types Supported:
 * - Chart.js: "column", "groupedColumn", "line", "multipleLine", "area", "pie"
 * - Plotly.js: "scatter", "multiplePies"
 *
 * Features:
 * - Automatically groups data by `hueFeature` if specified
 * - Supports responsive chart rendering
 * - Automatically destroys previous chart instances to prevent duplication/memory leaks
 * - Handles missing or undefined values with fallback logic
 *
 * @component
 *
 * @param {Object} props - Component props
 * @param {Array<Object>} props.dataset - The dataset to visualize, where each object is a row with key-value pairs
 * @param {string} props.xFeature - The key in the dataset used for the x-axis or pie grouping
 * @param {string} props.yFeature - The key in the dataset used for the y-axis or pie values
 * @param {string} [props.hueFeature] - Optional key used for grouping (categorical hue)
 * @param {string} props.chartType - Type of chart to render, one of:
 *   "column", "groupedColumn", "line", "multipleLine", "area", "pie", "scatter", "multiplePies"
 *
 * @example
 * <ChartViewer
 *   dataset={[
 *     { country: "USA", value: 100, region: "North America" },
 *     { country: "Canada", value: 80, region: "North America" },
 *     { country: "Germany", value: 120, region: "Europe" },
 *   ]}
 *   xFeature="country"
 *   yFeature="value"
 *   hueFeature="region"
 *   chartType="groupedColumn"
 * />
 *
 * @returns {JSX.Element} A chart visualization wrapped in a div with appropriate rendering logic
 */

Chart.register(ChartDataLabels); // Register ChartDataLabels plugin with Chart.js

const ChartViewer = ({ dataset, xFeature, yFeature, hueFeature, chartType }) => {
  const chartRef = useRef(null);
  const plotlyRef = useRef(null);

  const colors = ["red", "blue", "green", "orange", "purple", "cyan", "magenta", "yellow"];

  // Function to group dataset by a specific feature (e.g., hueFeature)
  const groupByFeature = (feature) => {
    const uniqueValues = [...new Set(dataset.map(item => item[feature] ?? "Unknown"))];
    return uniqueValues.map(label => ({
      label,
      data: dataset.filter(item => (item[feature] ?? "Unknown") === label),
    }));
  };

  // Clear existing charts to prevent memory leaks or duplication
  const clearCharts = () => {
    if (chartRef.current?.chartInstance) {
      chartRef.current.chartInstance.destroy();
    }
    if (plotlyRef.current) {
      Plotly.purge(plotlyRef.current);
    }
  };

  // เรียกใช้เมื่อมีการเปลี่ยนแปลงของ props
  // ทำหน้าที่จัดการการวาดกราฟใหม่
  useEffect(() => {
    clearCharts();

    const ctx = chartRef.current?.getContext("2d"); // context สำหรับวาด Chart.js
    const uniqueX = [...new Set(dataset.map(d => d[xFeature]))]; // ค่าที่ไม่ซ้ำกันบนแกน X
    const grouped = groupByFeature(hueFeature || xFeature || "group"); // ข้อมูลที่จัดกลุ่มตาม hueFeature หรือ xFeature

    if (["column", "groupedColumn", "line", "multipleLine", "area"].includes(chartType) && ctx) {
      let datasets;

      if (chartType === "multipleLine") {
        datasets = grouped.map((group, i) => {
          const dataMap = new Map(group.data.map(d => [d[xFeature], d[yFeature]]));
          const alignedData = uniqueX.map(x => dataMap.get(x) || 0);
          return {
            label: group.label,
            data: alignedData,
            backgroundColor: colors[i % colors.length],
            borderColor: colors[i % colors.length],
            fill: false,
          };
        });
      } else if (chartType === "groupedColumn") {
        datasets = grouped.map((group, i) => {
          const dataMap = new Map(group.data.map(d => [d[xFeature], d[yFeature]]));
          const alignedData = uniqueX.map(x => dataMap.get(x) || 0);
          return {
            label: group.label,
            data: alignedData,
            backgroundColor: colors[i % colors.length],
            borderColor: colors[i % colors.length],
            fill: chartType === "area",
          };
        });
      } else {
        const dataMap = new Map(dataset.map(d => [d[xFeature], d[yFeature]]));
        const alignedData = uniqueX.map(x => dataMap.get(x) || 0);
        datasets = [{
          label: yFeature,
          data: alignedData,
          backgroundColor: colors[0],
          borderColor: colors[0],
          fill: chartType === "area",
        }];
      }

      const config = {
        type: (chartType === "column" || chartType === "groupedColumn") ? "bar" : "line",
        data: {
          labels: uniqueX,
          datasets,
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            datalabels: {
              display: false,
            },
          },
        },
        plugins: [ChartDataLabels],
      };

      chartRef.current.chartInstance = new Chart(ctx, config);
    }

    if (chartType === "pie" && ctx && hueFeature) {
      const pieData = groupByFeature(hueFeature);
      const data = pieData.map((g) => yFeature
        ? g.data.reduce((sum, d) => sum + Number(d[yFeature] || 0), 0)
        : g.data.length
      );

      const config = {
        type: "pie",
        data: {
          labels: pieData.map((g) => g.label),
          datasets: [{ data, backgroundColor: colors }],
        },
        options: {
          plugins: {
            datalabels: {
              formatter: (value, context) => {
                const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                return ((value / total) * 100).toFixed(1) + "%";
              },
              color: "#fff",
            },
          },
        },
        plugins: [ChartDataLabels],
      };

      chartRef.current.chartInstance = new Chart(ctx, config);
    }

    if (plotlyRef.current) {
      if (chartType === "scatter") {
        const groups = groupByFeature(hueFeature);
        const traces = groups.map((group, i) => ({
          x: group.data.map(d => d[xFeature]),
          y: group.data.map(d => d[yFeature]),
          mode: "markers",
          type: "scatter",
          name: group.label,
          marker: {
            size: 10,
            color: colors[i % colors.length],
          },
        }));
        Plotly.newPlot(plotlyRef.current, traces, {
          title: "Scatter Plot",
          xaxis: { title: xFeature },
          yaxis: { title: yFeature },
        });
      }

      if (chartType === "multiplePies") {
        const pieGroups = groupByFeature(xFeature || hueFeature);
        const data = pieGroups.map((group, i) => {
          const categoryMap = new Map();
          group.data.forEach((d) => {
            const key = d[hueFeature];
            categoryMap.set(key, (categoryMap.get(key) || 0) + d[yFeature]);
          });
          return {
            type: "pie",
            values: Array.from(categoryMap.values()),
            labels: Array.from(categoryMap.keys()),
            domain: { row: 0, column: i },
            name: group.label,
            title: group.label,
          };
        });

        Plotly.newPlot(plotlyRef.current, data, {
          grid: { rows: 1, columns: pieGroups.length },
          title: "Multiple Pies Chart",
        });
      }
    }
  }, [chartType, dataset, xFeature, yFeature, hueFeature]);

  return (
    <div>
      <div style={{ width: "700px", height: "450px" }}>
        {["column", "groupedColumn", "line", "multipleLine", "area", "pie"].includes(chartType) && (
          <canvas ref={chartRef} width={700} height={450} />
        )}
        {["scatter", "multiplePies"].includes(chartType) && (
          <div ref={plotlyRef} style={{ width: "100%", height: "100%" }} />
        )}
      </div>
    </div>
  );
};

export default ChartViewer;
