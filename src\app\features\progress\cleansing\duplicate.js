import { createSlice } from "@reduxjs/toolkit";

/**
 * Initial state for handling duplicate data options.
 * 
 * @typedef {Object} DuplicateState
 * @property {string} selectedDuplicateOption - The selected mode or option for handling duplicates (e.g., "Remove", "HandleText").
 * @property {string} duplicateKeepOption - Option indicating which duplicates to keep (e.g., "first", "last").
 * @property {Array<string>} features - List of selected features (columns) used in "Remove Duplicates" mode.
 * @property {Array<string>} selectedCategoricalFeature - Selected categorical features used in "Handle Duplicates in text" mode.
 */ 
const initialState = {
    selectedDuplicateOption: "",
    removeDuplicatesFeatures: [], // เก็บ selected features ใน Remove Duplicates mode
    duplicateKeepOption: "",
    /*
    groupByFeatures = ["RainToday", "RainTomorrow"]
    */
    groupByFeatures: [], // collect group by features in 'Aggregate Duplicates' option
    /*
    aggregationSelections = {
        "RainToday": "mode",
        "RainTomorrow": "count"
    }
    */
    aggregationSelections: {},
    similarityMatric: "",
    selectedCategoricalFeature: [], // collect selected features in 'Handle Duplicates in Text' option 
    selectedTextSimilarityMethod: "", // collect selected text similarity method in 'Handle Duplicates in Text' option
};

/**
 * Redux slice for managing duplicate data handling options and selections.
 * 
 * @module duplicateSlice
 */
const duplicateSlice = createSlice({
    name: "duplicate",
    initialState,
    reducers: {
       
        // change initial state value
        setDuplicate: (state, action) => {
        return { ...state, ...action.payload };
      },

        /**
        * Resets the duplicate handling state to its initial default values.
        * 
        * @returns {DuplicateState} The initial state.
        * @example
        * dispatch(resetDuplicate());
        */
        resetDuplicate: () => {
            return initialState;
        }
    },
});

export const { setDuplicate, resetDuplicate } = duplicateSlice.actions;
export default duplicateSlice.reducer;
