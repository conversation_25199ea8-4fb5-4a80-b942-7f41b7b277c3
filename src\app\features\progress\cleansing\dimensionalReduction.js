import { createSlice } from "@reduxjs/toolkit";

/**
 * Initial state for the dimensional reduction feature.
 *
 * @typedef {Object} DimensionalReductionState
 * @property {string} selectedMethod - The dimensionality reduction method selected (e.g., 'PCA', 'UMAP').
 * @property {number} pcaComponents - Number of principal components to keep for PCA.
 * @property {boolean} applyWhitening - Whether to apply whitening in PCA.
 * @property {number} umapNeighbors - Number of neighbors parameter for UMAP algorithm.
 * @property {number} umapMinDist - Minimum distance parameter for UMAP.
 * @property {number} outputDimensions - Target output dimensions after reduction(UMAP).
 * @property {Array<string>} selectedNumericalFeatures - List of numerical feature names selected for reduction.
 */

const initialState = {
  selectedMethod: "",
  pcaComponents: 2,
  applyWhitening: false,
  umapNeighbors: 15,
  umapMinDist: 0.1,
  outputDimensions: 2,
  selectedNumericalFeatures: [],
};

/**
 * Redux slice for managing dimensionality reduction parameters and selections.
 *
 * @module dimensionalReductionSlice
 */

const dimensionalReductionSlice = createSlice({
  name: "dimensionalReduction",
  initialState,
  reducers: {
    /**
     * Sets multiple dimensional reduction parameters in the state.
     * Only updates keys that exist and are defined in the payload.
     *
     * @param {DimensionalReductionState} state - Current slice state.
     * @param {Object} action - Redux action object.
     * @param {Object} action.payload - Partial state update object.
     * @example
     * dispatch(setDimensionalReduction({ selectedMethod: 'PCA', pcaComponents: 3 }));
     */
    setDimensionalReduction(state, action) {
      const payload = action.payload;
      for (const key in payload) {
        if (payload[key] !== undefined) {
          state[key] = payload[key];
        }
      }
    },

    /**
     * Resets the dimensional reduction state to its initial default values.
     *
     * @returns {DimensionalReductionState} The initial state.
     * @example
     * dispatch(resetDimensionalReduction());
     */
    resetDimensionalReduction() {
      return initialState;
    },
  },
});

export const { setDimensionalReduction, resetDimensionalReduction } =
  dimensionalReductionSlice.actions;
export default dimensionalReductionSlice.reducer;
