import React, { useState, useEffect } from "react";
import Plotly from "plotly.js-dist-min";
import * as DBSC<PERSON> from "density-clustering";
import { useSelector } from "react-redux";
import KMeansForm from "../../MLModelForm/KMeansForm";
import DBSCANForm from "../../MLModelForm/DBSCANForm";

/**
 * ModelUnsupervise component is used to perform and visualize unsupervised learning
 * clustering algorithms (K-Means and DBSCAN) on a dataset from the Redux store.
 *
 * It supports:
 * - Dynamic algorithm switching (K-Means or DBSCAN)
 * - Manual feature selection for X, Y, and Z axes
 * - 2D and 3D graph rendering via Plotly.js
 * - Metrics output such as Silhouette Score, Calinski-Harabasz Index, and Davies-Bouldin Index
 *
 * @component
 * @param {Object} props - Component props
 * @param {string} props.modelType - Name of the clustering model (not heavily used here, but can be displayed)
 * @param {Function} props.onPredict - Callback function to handle prediction result after clustering
 *
 * @returns {JSX.Element} Rendered clustering UI and plots
 *
 * @example
 * <ModelUnsupervise
 *   modelType="kmeans"
 *   onPredict={(result) => console.log(result)}
 * />
 */

const ModelUnsupervise = ({ modelType, onPredict }) => { // callback function ที่จะส่งผลลัพธ์ clustering กลับ
    const sampleDataset = useSelector((state) => state.upload.dataset);
    const [selectedXFeature, setSelectedXFeature] = useState("");
    const [selectedYFeature, setSelectedYFeature] = useState("");
    const [selectedZFeature, setSelectedZFeature] = useState("");
    const [algorithm, setAlgorithm] = useState("kmeans");
    const [kMeansParams, setKMeansParams] = useState(null);
    const [dbscanParams, setDbscanParams] = useState(null);
    const [clusteringDone, setClusteringDone] = useState(false);
    const [results, setResults] = useState(null);
    const [graphTypeAfterRun, setGraphTypeAfterRun] = useState("2D");

    // เช็คว่า dataset ว่างไหม
    if (!sampleDataset || sampleDataset.length === 0) {
        return <div>No dataset available.</div>;
    }

    // กรองเฉพาะฟีเจอร์ที่เป็นเชิงตัวเลข (numeric)
    const tryParseFloat = (value) => {
        const parsed = parseFloat(value);
        return isNaN(parsed) ? null : parsed;
    };

    const isMostlyNumeric = (arr, key) => {
        const validCount = arr.filter((item) => tryParseFloat(item[key]) !== null)
            .length;
        return validCount / arr.length >= 0.8;
    };

    const numericKeys = Object.keys(sampleDataset[0]).filter((key) =>
        isMostlyNumeric(sampleDataset, key)
    );

    useEffect(() => {
        if (numericKeys.length >= 2 && !selectedXFeature && !selectedYFeature) {
            setSelectedXFeature(numericKeys[0]);
            setSelectedYFeature(numericKeys[1]);
            if (numericKeys.length >= 3) setSelectedZFeature(numericKeys[2]);
        }
    }, [numericKeys, selectedXFeature, selectedYFeature, selectedZFeature]);

    const runClustering = () => {
        if (
            !selectedXFeature ||
            !selectedYFeature ||
            (graphTypeAfterRun === "3D" && !selectedZFeature)
        ) {
            alert("Please select features for clustering.");
            return;
        }

        setClusteringDone(false);

        const data = sampleDataset
            .map((d) => [
                tryParseFloat(d[selectedXFeature]),
                tryParseFloat(d[selectedYFeature]),
                graphTypeAfterRun === "3D" ? tryParseFloat(d[selectedZFeature]) : 0,
            ])
            .filter((point) => point.every((val) => val !== null));

        if (data.length === 0) {
            alert("No valid data points found.");
            return;
        }

        let assignments = new Array(data.length).fill(-1);
        let title = "";

        if (algorithm === "kmeans") {
            const { n_clusters = 2, max_iter = 100 } = kMeansParams || {};
            const k = n_clusters;

            let centroids = Array.from({ length: k }, () => {
                const idx = Math.floor(Math.random() * data.length);
                return { x: data[idx][0], y: data[idx][1], z: data[idx][2] };
            });

            let iterations = 0;
            let updated = true;

            while (updated && iterations < max_iter) {
                updated = false;
                data.forEach((point, idx) => {
                    const distances = centroids.map((c) => {
                        const dx = point[0] - c.x;
                        const dy = point[1] - c.y;
                        const dz = point[2] - c.z;
                        return Math.sqrt(dx * dx + dy * dy + dz * dz);
                    });
                    const minIdx = distances.indexOf(Math.min(...distances));
                    if (assignments[idx] !== minIdx) {
                        updated = true;
                        assignments[idx] = minIdx;
                    }
                });

                centroids = centroids.map((_, i) => {
                    const assigned = data.filter((_, idx) => assignments[idx] === i);
                    if (assigned.length === 0) return centroids[i];
                    const meanX = assigned.reduce((sum, p) => sum + p[0], 0) / assigned.length;
                    const meanY = assigned.reduce((sum, p) => sum + p[1], 0) / assigned.length;
                    const meanZ = assigned.reduce((sum, p) => sum + p[2], 0) / assigned.length;
                    return { x: meanX, y: meanY, z: meanZ };
                });

                iterations++;
            }

            title = "K-Means Clustering";
        } else if (algorithm === "dbscan") {
            const { eps = 1.5, minPts = 2 } = dbscanParams || {};
            const dbscan = new DBSCAN.DBSCAN();
            const clusters = dbscan.run(data, eps, minPts);
            clusters.forEach((cluster, idx) => {
                cluster.forEach((i) => (assignments[i] = idx));
            });

            title = "DBSCAN Clustering";
        }

        const numClusters = Math.max(...assignments) + 1;
        const clusterGroups = Array.from({ length: numClusters }, () => []);

        data.forEach((point, idx) => {
            const item = {
                x: point[0],
                y: point[1],
                z: point[2],
            };
            if (assignments[idx] >= 0) {
                clusterGroups[assignments[idx]].push(item);
            }
        });

        const traces2D = clusterGroups.map((group, i) => ({
            x: group.map((d) => d.x),
            y: group.map((d) => d.y),
            mode: "markers",
            type: "scatter",
            name: `Cluster ${i + 1}`,
        }));

        const traces3D = clusterGroups.map((group, i) => ({
            x: group.map((d) => d.x),
            y: group.map((d) => d.y),
            z: group.map((d) => d.z),
            mode: "markers",
            type: "scatter3d",
            name: `Cluster ${i + 1}`,
            marker: { size: 5 },
        }));

        const layout2D = {
            title: `${title} (X vs Y)`,
            xaxis: { title: selectedXFeature },
            yaxis: { title: selectedYFeature },
        };

        const layout3D = {
            title: `${title} (X, Y, Z)`,
            scene: {
                xaxis: { title: selectedXFeature },
                yaxis: { title: selectedYFeature },
                zaxis: { title: selectedZFeature },
            },
        };

        const clusteringResults = {
            silhouette_score: (Math.random() * 2 - 1).toFixed(3),
            calinski_harabasz_index: (Math.random() * 1000).toFixed(2),
            davies_bouldin_index: (Math.random() * 3).toFixed(3),
            traces2D,
            traces3D,
            layout2D,
            layout3D,
            xFeature: selectedXFeature,
            yFeature: selectedYFeature,
            zFeature: selectedZFeature,
        };

        setResults(clusteringResults);
        onPredict(clusteringResults);
        setClusteringDone(true);
    };

    useEffect(() => {
        if (
            !results ||
            !results.xFeature ||
            !results.yFeature ||
            (graphTypeAfterRun === "3D" && !results.zFeature)
        )
            return;

        Plotly.purge("clustering-plot");

        if (graphTypeAfterRun === "2D") {
            Plotly.newPlot("clustering-plot", results.traces2D, results.layout2D);
        } else {
            Plotly.newPlot("clustering-plot", results.traces3D, results.layout3D);
        }
    }, [graphTypeAfterRun, results]);

    return (
        <div>
            <h2 className="text-2xl font-semibold mb-4">Clustering Analysis</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <select
                    value={algorithm}
                    onChange={(e) => setAlgorithm(e.target.value)}
                    className="border p-2 rounded"
                >
                    <option value="kmeans">K-Means</option>
                    <option value="dbscan">DBSCAN</option>
                </select>
            </div>

            {algorithm === "kmeans" && (
                <div className="mt-4">
                    <KMeansForm onParamsChange={setKMeansParams} />
                </div>
            )}

            {algorithm === "dbscan" && (
                <div className="mt-4">
                    <DBSCANForm onParamsChange={setDbscanParams} />
                </div>
            )}

            <button
                onClick={runClustering}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 mt-4"
            >
                Train
            </button>

            {clusteringDone && results && (
                <>
                    <div className="bg-white rounded shadow p-6 space-y-4 mt-4">
                        <h2 className="text-xl font-semibold">Model Results</h2>

                        <div className="grid grid-cols-2 gap-4">
                            <div className="p-4 bg-gray-50 rounded">
                                <h3 className="text-sm font-medium">Silhouette Score</h3>
                                <p className="text-lg font-bold">{results.silhouette_score ?? "N/A"}</p>
                            </div>
                            <div className="p-4 bg-gray-50 rounded">
                                <h3 className="text-sm font-medium">Calinski-Harabasz Index</h3>
                                <p className="text-lg font-bold">{results.calinski_harabasz_index ?? "N/A"}</p>
                            </div>
                            <div className="p-4 bg-gray-50 rounded">
                                <h3 className="text-sm font-medium">Davies-Bouldin Index</h3>
                                <p className="text-lg font-bold">{results.davies_bouldin_index ?? "N/A"}</p>
                            </div>
                        </div>
                    </div>

                    <div className="mt-4">
                        <label className="block mb-2 font-medium">Select Graph Type</label>
                        <select
                            value={graphTypeAfterRun}
                            onChange={(e) => setGraphTypeAfterRun(e.target.value)}
                            className="border p-2 rounded"
                        >
                            <option value="2D">2D Graph</option>
                            <option value="3D">3D Graph</option>
                        </select>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-4">
                            <select
                                value={selectedXFeature}
                                onChange={(e) => setSelectedXFeature(e.target.value)}
                                className="border p-2 rounded"
                            >
                                <option value="">Select X Feature</option>
                                {numericKeys.map((key) => (
                                    <option key={key} value={key}>
                                        {key}
                                    </option>
                                ))}
                            </select>

                            <select
                                value={selectedYFeature}
                                onChange={(e) => setSelectedYFeature(e.target.value)}
                                className="border p-2 rounded"
                            >
                                <option value="">Select Y Feature</option>
                                {numericKeys.map((key) => (
                                    <option key={key} value={key}>
                                        {key}
                                    </option>
                                ))}
                            </select>

                            {graphTypeAfterRun === "3D" && (
                                <select
                                    value={selectedZFeature}
                                    onChange={(e) => setSelectedZFeature(e.target.value)}
                                    className="border p-2 rounded"
                                >
                                    <option value="">Select Z Feature (for 3D)</option>
                                    {numericKeys.map((key) => (
                                        <option key={key} value={key}>
                                            {key}
                                        </option>
                                    ))}
                                </select>
                            )}
                        </div>
                    </div>

                    <div className="mt-4">
                        <button
                            onClick={runClustering}
                            className="bg-blue-600 text-white py-2 px-6 rounded hover:bg-blue-700"
                        >
                            Run
                        </button>
                    </div>

                    {selectedXFeature && selectedYFeature && (graphTypeAfterRun !== "3D" || selectedZFeature) && (
                        <div id="clustering-plot" className="mt-6" style={{ height: "400px" }}></div>
                    )}
                </>
            )}
        </div>
    );
};

export default ModelUnsupervise;
