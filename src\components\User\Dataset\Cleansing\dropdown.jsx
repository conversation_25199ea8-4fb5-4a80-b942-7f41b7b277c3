import React, { useState, useRef, useEffect } from "react";

export default function Dropdown({
  options = [], // <-- array of options
  placeholder = "Select an option", // <-- placeholder text
  tell = "Select an option", // <-- tell text
  searchPlaceholder = "Search...", // <-- search placeholder text
  setValue, // <-- function to set the value
  value, // <-- value
}) {
/** 
   * Controls the visibility of the dropdown menu
   * @type {[boolean, function]} - [isOpen, setIsOpen]
   * @property {boolean} isOpen - Current state of dropdown visibility
   * @property {function} setIsOpen - Function to toggle dropdown visibility
   */
const [isOpen, setIsOpen] = useState(false);

  /**
   * State for storing the search query
   * @type {[string, function]} - [searchQuery, setSearchQuery]
   * @property {string} searchQuery - Current search query
   * @property {function} setSearchQuery - Function to update search query
   */
  const [searchQuery, setSearchQuery] = useState("");

  /**
   * Ref for the dropdown menu
   * @type {[React.RefObject, function]} - [dropdownRef, setDropdownRef]
   * @property {React.RefObject} dropdownRef - Ref for the dropdown menu
   * @property {function} setDropdownRef - Function to update the dropdown ref
   */
  const dropdownRef = useRef(null);

  /**
   * Ref for the search input
   * @type {[React.RefObject, function]} - [searchInputRef, setSearchInputRef]
   * @property {React.RefObject} searchInputRef - Ref for the search input
   * @property {function} setSearchInputRef - Function to update the search input ref
   */
  const searchInputRef = useRef(null);

  

  // Filter options based on search query
  const filteredOptions = options.filter((option) =>
    option.toLowerCase().includes(searchQuery.toLowerCase())
  );

  /**
   * Effect to automatically focus the search input when the dropdown opens
   * This improves UX by allowing immediate typing without clicking the input
   * Dependencies: [isOpen] - Only runs when dropdown open state changes
   */
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);
  // console.log("searchInputRef.current :", searchInputRef.current);

  /**
   * Effect to handle clicking outside the dropdown
   * Closes the dropdown and clears search when clicking anywhere outside
   * Uses event delegation to listen for mousedown events on the document
   * Cleanup function removes event listener when component unmounts
   */
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
        setSearchQuery("");
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleSelect = (option) => {
    if (setValue) setValue(option);
    setIsOpen(false);
    setSearchQuery("");
  };

  return (
    <div className="mb-4">
      <h1 className="font-bold mb-4 text-xl">{tell}</h1>
      <div className="relative w-full max-w-xs" ref={dropdownRef}>
        {/* Dropdown trigger button */}
        <button
          type="button"
          onClick={() => setIsOpen((prev) => !prev)}
          className="flex w-full items-center justify-between rounded-full bg-white px-5 py-3 text-left shadow-md transition-all hover:shadow-lg focus:outline-none"
        >
          <span className="text-gray-700">{value || placeholder}</span>
          <span className="font-bold text-black">V</span>
        </button>

        {/* Dropdown menu with search */}
        {isOpen && (
          <div className="z-40 absolute mt-2 w-full rounded-lg bg-white py-2 shadow-xl">
            {/* Search input */}
            <div className="px-3 pb-2">
              <input
                ref={searchInputRef}
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={searchPlaceholder}
                className="w-full rounded-md border border-gray-200 px-3 py-2 text-sm focus:border-gray-300 focus:outline-none"
              />
            </div>
            <div className="z-40 my-1 border-t border-gray-100"></div>
            {/* Options list */}
            <div className="z-40 max-h-60 overflow-y-auto">
              {filteredOptions.length > 0 ? (
                filteredOptions.map((option, index) => (
                  <div
                    key={index}
                    onClick={() => handleSelect(option)}
                    className="cursor-pointer px-5 py-2 text-gray-700 hover:bg-gray-100"
                  >
                    {option}
                  </div>
                ))
              ) : (
                <div className="px-5 py-2 text-gray-500 italic">
                  No results found
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
