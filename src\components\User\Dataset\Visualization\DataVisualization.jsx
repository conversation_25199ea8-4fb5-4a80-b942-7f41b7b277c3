import React, { useState } from "react";
import <PERSON><PERSON><PERSON><PERSON> from "./ChartViewer";
import Sidebar from "./Sidebar";
import sampleDataset from "../../../../utils/sampleDataset";
import { useSelector } from "react-redux";

/**
 * DataVisualization is a React component that allows users to dynamically
 * visualize datasets using different chart types, axes, and optional hue grouping.
 * 
 * It integrates:
 * - A Sidebar component for selecting chart configuration (chart type, x, y, hue)
 * - A ChartViewer component to render the actual chart based on selected features
 * - Redux state to retrieve the currently uploaded dataset
 * 
 * Features:
 * - Automatically adapts to available dataset features
 * - Updates chart dynamically based on user selection
 * - Supports multiple chart types including bar, line, scatter, pie, etc.
 * 
 * @component
 *
 * @returns {JSX.Element} The full data visualization interface, including sidebar and chart area
 *
 * @example
 * // This component is typically used inside a dashboard or data analysis module
 * <DataVisualization />
 */

const DataVisualization = () => {
  const dataset = useSelector((state) => state.upload.dataset);

  // เก็บสถานะของการเลือกประเภทกราฟ(chartType) และฟีเจอร์แกน x, y, และ hue
  const [selections, setSelections] = useState({
    chartType: "column",
    x: "",
    y: "",
    hue: "",
  });

  /**
  * Updates the user's selected chart configuration.
  *
  * @function
  * @param {string} key - The key to update ('chartType', 'x', 'y', or 'hue')
  * @param {string} value - The new value for the selected key
  */
  const handleSelectionChange = (key, value) => {
    setSelections((prev) => ({ ...prev, [key]: value }));
  };

  // ถ้ามีข้อมูลใน dataset จะดึงชื่อ key (หรือชื่อคอลัมน์) ของข้อมูลนั้นมาแสดงใน dropdown
  const features = dataset.length ? Object.keys(dataset[0]) : [];

  return (
    <div className="flex space-x-6 p-6">
      {/* Sidebar handles all dropdowns */}
      <Sidebar
        features={features}
        selections={selections}
        onSelectionChange={handleSelectionChange}
      />

      {/* Main Chart Section */}
      <div className="flex-1 bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-semibold mb-4">Data Visualization</h2>

        {/* Chart Viewer renders chart based on full selection */}
        <ChartViewer
          dataset={dataset}
          chartType={selections.chartType}
          xFeature={selections.x}
          yFeature={selections.y}
          hueFeature={selections.hue}
        />
      </div>
    </div>
  );
};

export default DataVisualization;
