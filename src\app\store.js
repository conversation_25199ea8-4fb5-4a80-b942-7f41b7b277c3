import { configureStore } from "@reduxjs/toolkit";
import userRoleReducer from "./features/login/userRoleSlice"; // Import the userRole reducer
import progressReducer from "./features/progress/progressSlice"; // Import the progress reducer
import uploadReducer from "./features/progress/upload/uploadSlice"; // Import the upload reducer
import missingValueReducer from "./features/progress/cleansing/missingValue"; // Import the missingValue reducer
import outlierReducer from "./features/progress/cleansing/outlier"; // Import the outlier reducer
import typoValueReducer from "./features/progress/cleansing/typo"; // Import the typoValue reducer
import duplicateReducer from "./features/progress/cleansing/duplicate"; // Import the duplicate reducer
import encodingReducer from "./features/progress/cleansing/encoding"; // Import the encoding reducer
import scalingReducer from "./features/progress/cleansing/scaling"; // Import the scaling reducer
import dimensionReducer from "./features/progress/cleansing/dimensionalReduction"; // Import the dimension reducer

export const store = configureStore({
  reducer: {
    userRole: userRoleReducer, // Add the userRole reducer here
    progress: progressReducer, // Add the progress reducer here
    upload: uploadReducer, // Add the upload reducer here
    missingValue: missingValueReducer, // Add the missingValue reducer here
    outlier: outlierReducer, // Add the outlier reducer here
    typoValue: typoValueReducer,
    duplicate: duplicateReducer, // Add the outlier reducer here
    encoding: encodingReducer, // Add the duplicate reducer here
    scaling: scalingReducer, // Add the scaling reducer here
    dimensionalReduction: dimensionReducer, // Add the dimension reducer here
  },
});
