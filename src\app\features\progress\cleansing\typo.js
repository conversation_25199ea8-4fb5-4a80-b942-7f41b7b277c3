import { createSlice } from "@reduxjs/toolkit";
 
const initialState = {
  correctionsRedux: {
    column: "", // Collect column name that user select
    corrections: {}, // object of {oldValue : "newValue"}
  },
};

export const typoValueSlice = createSlice({
  name: "typoValue",
  initialState,
  reducers: {
    setTypoValue: (state, action) => {
      return { ...state, ...action.payload };
    },
    resetTypoValue: () => {
      return initialState;
    },
  },
});

export const { setTypoValue, resetTypoValue } = typoValueSlice.actions;
export default typoValueSlice.reducer;
