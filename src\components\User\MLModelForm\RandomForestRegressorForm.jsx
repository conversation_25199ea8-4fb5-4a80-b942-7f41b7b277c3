import React, { useState, useEffect } from "react";

const RandomForestRegressorForm = ({ onParamsChange }) => {
    const [params, setParams] = useState({
        n_estimators: 100,
        max_features: "sqrt",
        min_samples_split: 2,
    });

    useEffect(() => {
        onParamsChange(params);
    }, [params, onParamsChange]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setParams((prevParams) => ({
            ...prevParams,
            [name]: value,
        }));
    };

    return (
        <div className="space-y-2 p-4 bg-white rounded shadow">

            <div>
                <label className="block text-sm font-medium">n_estimators:</label>
                <input
                    type="number"
                    name="n_estimators"
                    value={params.n_estimators}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                    step="100"
                />
            </div>

            <div>
                <label className="block text-sm font-medium">max_features:</label>
                <select
                    name="max_features"
                    value={params.max_features}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                >
                    <option value="sqrt">sqrt</option>
                    <option value="log2">log2</option>
                </select>
            </div>

            <div>
                <label className="block text-sm font-medium">min_samples_split:</label>
                <input
                    type="number"
                    name="min_samples_split"
                    value={params.min_samples_split}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                    step="1"
                />
            </div>
        </div>
    );
};

export default RandomForestRegressorForm;
