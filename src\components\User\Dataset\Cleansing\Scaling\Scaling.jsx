/* -------------------------------------------------------
 * The Scaling component allows users to apply scaling methods
 * to features in their dataset, such as standardization
 * or min-max normalization.
 *
 * It interacts with the Redux store to manage state and updates,
 * and provides a UI to select the scaling method and features.
 *
 * It also includes handling for specifying min and max ranges
 * when using Min-Max Scaling.
 * ------------------------------------------------------- */
import React, { useEffect, useState, useMemo } from "react";
import { useSelector } from "react-redux";
import ApplyTable from "../ApplyTable";

const cleanUniqueId = (str) => str.replace(/-\d+$/, "");

const scalingOptions = ["Standardization", "Min-Max Scaling"];

const Scaling = () => {
  // ========= Fetch feature lists from Redux =========
  const reduxFeatures = useSelector((state) => state.upload.features);
  // Redux state for pull dataset
  const dataset = useSelector((state) => state.upload.dataset);

  // Use useMemo to prevent re-creating the numerical items list on every render
  const numericalItems = useMemo(
    () =>
      (reduxFeatures?.numerical?.items || []).map((item) => ({
        ...item,
      })),
    [reduxFeatures?.numerical?.items]
  );

  // Create a map for quick lookup of numerical features by ID
  const allNumericalItemsMap = Object.fromEntries(
    numericalItems.map((item) => [item.id, item])
  );

  // Get the order of all features from Redux
  const allFeatureOrder =
    reduxFeatures?.allFeatureOrder || numericalItems.map((n) => n.id);

  // ========= Local state for UI =========
  const [scalingMethod, setScalingMethod] = useState("");
  const [selectedFeatures, setSelectedFeatures] = useState([]);

  /**
   * @type {Object.<string, Object>} featureRanges
   * Maps feature IDs to min/max ranges for Min-Max Scaling
   * @example
   * {
   * "feature-id-1": { min: 0, max: 1 },
   * "feature-id-2": { min: 0, max: 1 },
   */
  const [featureRanges, setFeatureRanges] = useState({});
  // State for toggle show apply table
  const [showTable, setShowTable] = useState(false);

  // ========= Handlers =========
  // Handles changes to the scaling method dropdown
  const handleScalingChange = (value) => {
    setScalingMethod(value);
    // Clear selections and ranges when the method changes
    setSelectedFeatures([]);
  };

  // Toggles the selection of a feature
  const handleFeatureToggle = (featureId) => {
    const updated = selectedFeatures.includes(featureId)
      ? selectedFeatures.filter((fid) => fid !== featureId)
      : [...selectedFeatures, featureId];

    setSelectedFeatures(updated);
  };

  // Handles changes to the min/max input fields for Min-Max Scaling
  const handleMinMaxChange = (featureId, type, value) => {
    const parsedValue = parseFloat(value);
    const current = featureRanges[featureId] || { min: 0, max: 1 };
    const newRange = {
      ...current,
      [type]: isNaN(parsedValue) ? (type === "min" ? 0 : 1) : parsedValue,
    };

    // Keep min value <= max value
    if (type === "min" && newRange.min >= newRange.max) {
      newRange.max = newRange.min + 1;
    }
    if (type === "max" && newRange.max <= newRange.min) {
      newRange.min = newRange.max - 1;
    }

    const updatedRanges = { ...featureRanges, [featureId]: newRange };
    setFeatureRanges(updatedRanges);
  };

  // Show table when apply button is clicked
  const handleApply = () => {
    setShowTable(true);
  };

  // Close the table and reset the parameters when reset button is clicked
  const handleResetParameters = () => {
    setScalingMethod("");
    setSelectedFeatures([]);
    setFeatureRanges({});
    setShowTable(false);
  };

  // Fix: This useEffect now correctly adds new ranges and removes stale ones
  useEffect(() => {
    if (scalingMethod === "Min-Max Scaling") {
      setFeatureRanges((prevRanges) => {
        const newRanges = {};
        selectedFeatures.forEach((featureId) => {
          // Use the existing range if it exists, otherwise use the default
          newRanges[featureId] = prevRanges[featureId] || { min: 0, max: 1 };
        });
        return newRanges;
      });
    }
  }, [selectedFeatures, scalingMethod]);

  useEffect(() => {
    console.log("selectedFeatures: ", selectedFeatures);
    console.log("featureRanges: ", featureRanges);
  }, [selectedFeatures, featureRanges]);

  // ========= Render =========
  return (
    <div className="mx-4 max-w-7xl">
      <div className="p-8 bg-gray-50 rounded-2xl shadow-lg border border-gray-200">
        <h2 className="text-2xl font-bold text-gray-800 mb-6 border-b pb-4">
          Scaling and Normalization
        </h2>
        <div className="mb-8">
          <label className="block text-lg font-semibold text-gray-700 mb-3">
            Select Scaling Method
          </label>
          <div className="flex gap-4">
            {scalingOptions.map((option) => (
              <button
                key={option}
                className={`px-6 py-3 rounded-full text-lg font-semibold transition-colors duration-300 ${
                  scalingMethod === option
                    ? "bg-blue-600 text-white shadow-lg"
                    : "bg-white text-blue-600 border border-blue-600 hover:bg-blue-50"
                }`}
                onClick={() => handleScalingChange(option)}
              >
                {option}
              </button>
            ))}
          </div>
        </div>

        {/* Conditional rendering for feature selection */}
        {(scalingMethod === "Standardization" ||
          scalingMethod === "Min-Max Scaling") &&
          allFeatureOrder.length > 0 && (
            <div className="mt-6">
              <h3 className="text-lg font-semibold text-gray-700 mb-3">
                Select Numeric Features to Scale
              </h3>
              <p className="text-sm text-gray-500 mb-4">
                Choose the features you want to apply the scaling method to.
              </p>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                {allFeatureOrder
                  .filter(
                    (id) =>
                      allNumericalItemsMap[id] &&
                      !allNumericalItemsMap[id].disabled &&
                      numericalItems.some((n) => n.id === id)
                  )
                  .map((id) => {
                    const item = allNumericalItemsMap[id];
                    const range = featureRanges[id] || { min: 0, max: 1 };

                    return (
                      <div
                        key={item.id}
                        className="border p-4 rounded-lg bg-gray-100 shadow-sm"
                      >
                        <div className="flex items-center mb-2">
                          <input
                            type="checkbox"
                            id={item.id}
                            className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            checked={selectedFeatures.includes(item.id)}
                            onChange={() => handleFeatureToggle(item.id)}
                          />
                          <label
                            htmlFor={item.id}
                            className="text-gray-700 font-medium"
                          >
                            {item.feature}
                          </label>
                        </div>

                        {selectedFeatures.includes(item.id) &&
                          scalingMethod === "Min-Max Scaling" && (
                            <div className="grid grid-cols-2 gap-4 mt-2">
                              <div>
                                <label className="block text-sm text-gray-600 mb-1">
                                  Min Value
                                </label>
                                <input
                                  type="number"
                                  value={range.min}
                                  onChange={(e) =>
                                    handleMinMaxChange(
                                      item.id,
                                      "min",
                                      e.target.value
                                    )
                                  }
                                  className="w-full px-2 py-1 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="0"
                                />
                              </div>
                              <div>
                                <label className="block text-sm text-gray-600 mb-1">
                                  Max Value
                                </label>
                                <input
                                  type="number"
                                  value={range.max}
                                  onChange={(e) =>
                                    handleMinMaxChange(
                                      item.id,
                                      "max",
                                      e.target.value
                                    )
                                  }
                                  className="w-full px-2 py-1 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="1"
                                />
                              </div>
                            </div>
                          )}
                      </div>
                    );
                  })}
              </div>
            </div>
          )}
      </div>

      {/* apply and reset parameter button */}
      {/* Button for applying the selected method and resetting the parameters */}
      <div className="flex flex-col items-center gap-5 p-5 w-full">
        <div className="flex gap-5">
          <button
            className="bg-[#a4c2f4] text-black py-4 px-6 text-lg rounded min-w-[250px] transition-colors hover:bg-[#8ab4f8] active:bg-[#7aa0e0]"
            onClick={handleApply}
          >
            Apply Typo Correction
          </button>
          <button
            className="bg-[#a4c2f4] text-black py-4 px-6 text-lg rounded min-w-[250px] transition-colors hover:bg-[#8ab4f8] active:bg-[#7aa0e0]"
            onClick={handleResetParameters}
          >
            Reset Parameters
          </button>
        </div>
      </div>

      {/* Table show apply result */}
      {/* Show the table if showTable is true */}
      {showTable && (
        <ApplyTable
          originalData={dataset}
          cleanedData={dataset}
          tab="Duplicate"
        />
      )}
    </div>
  );
};

export default Scaling;
