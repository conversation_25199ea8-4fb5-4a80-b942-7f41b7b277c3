import React, { useState, useEffect } from "react";

const AdaBoostForm = ({ onParamsChange }) => {
    const [params, setParams] = useState({
        n_estimators: 50,
        learning_rate: 0.1,
        algorithm: "samme.r",
    });

    useEffect(() => {
        onParamsChange(params);
    }, [params, onParamsChange]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setParams((prevParams) => ({
            ...prevParams,
            [name]: value,
        }));
    };

    return (
        <div className="space-y-2 p-4 bg-white rounded shadow">

            <div>
                <label className="block text-sm font-medium">n_estimators:</label>
                <input
                    type="number"
                    name="n_estimators"
                    value={params.n_estimators}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                    step="10"
                />
            </div>

            <div>
                <label className="block text-sm font-medium">learning_rate:</label>
                <input
                    type="number"
                    name="learning_rate"
                    value={params.learning_rate}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                    step="0.1"
                />
            </div>

            <div>
                <label className="block text-sm font-medium">algorithm:</label>
                <select
                    name="algorithm"
                    value={params.algorithm}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                >
                    <option value="samme">SAMME</option>
                    <option value="samme.r">SAMME.R</option>
                </select>
            </div>
        </div>
    );
};

export default AdaBoostForm;
