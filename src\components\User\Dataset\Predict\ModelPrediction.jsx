import React, { useState } from "react";
import { useSelector } from "react-redux";
import ModelPredictionForm from "./ModelPredictionForm";
import ModelResults from "./ModelResults";
import ModelUnsupervise from "./ModelUnsupervise";
import PredictFeatureInputList from "./PredictFeatureInputList";

/**
 * ModelPrediction component for handling supervised and unsupervised machine learning workflows.
 *
 * This component renders different interfaces based on whether the user is performing
 * supervised or unsupervised learning. It manages feature inputs, target selection,
 * model prediction results, and renders results dynamically.
 *
 * Redux State Dependencies:
 * - `state.upload.features`: Contains the categorized features (numerical, nominal, ordinal, unassigned)
 *   used to render input forms and build mappings.
 *
 * @component
 * @param {Object} props - Component props
 * @param {string} props.modelType - The type of ML model selected by the user (e.g., "Logistic Regression")
 * @returns {JSX.Element}
 *
 * @example
 * <ModelPrediction modelType="Logistic Regression" />
 */
const ModelPrediction = ({ modelType }) => {
  // ดึง features จาก Redux store ที่นี่เลย
  const features = useSelector((state) => state.upload.features);
  /**
  * Map of ordinal features to their ordered unique values.
  * Example:
  * {
  *   "education_level": ["primary", "secondary", "bachelor", "master"]
  * }
  */

  // สร้าง ordinalMap ตามใน ModelPredictionForm
  const ordinalItems = features?.ordinal?.items || [];
  const ordinalMap = {};
  ordinalItems.forEach((item) => {
    ordinalMap[item.feature] = item.uniqueValue || [];
  });

  /**
  * Mapping of all features by their unique `id`.
  * Example:
  * {
  *   "f123": { id: "f123", feature: "age", ... },
  *   "f124": { id: "f124", feature: "income", ... }
  * }
  */
  // สร้าง allItemsMap
  const allItemsMap = {};
  [
    ...(features?.unassigned?.items || []),
    ...(features?.numerical?.items || []),
    ...(features?.nominal?.items || []),
    ...(features?.ordinal?.items || []),
  ].forEach((item) => {
    allItemsMap[item.id] = item;
  });

  /**
   * Array of feature IDs, in the desired order for input rendering.
   * Falls back to keys of `allItemsMap` if `allFeatureOrder` is not defined.
   * @type {string[]}
   */
  // allFeatureOrder จาก features หรือ fallback เป็น keys ของ allItemsMap
  // ถ้า features.allFeatureOrder มีค่า ใช้ลำดับนั้น
  const allFeatureOrder = features?.allFeatureOrder || Object.keys(allItemsMap);

  // State for prediction results
  const [results, setResults] = useState(null);

  // Switch between Supervised and Unsupervised workflows
  const [isUnsupervised, setIsUnsupervised] = useState(false);

  // Stores selected feature values used for prediction input
  const [selectedFeatureValues, setSelectedFeatureValues] = useState({});

  // Selected target column for supervised learning
  const [targetColumn, setTargetColumn] = useState("");

  /**
   * Callback handler when prediction results are returned from backend or computed.
   *
   * @param {any} modelResults - Result object returned from the model prediction
   */
  const handlePrediction = (modelResults) => {
    setResults(modelResults);
  };

  return (
    <div className="space-y-4">
      <h1 className="text-2xl font-bold">
        Machine Learning Prediction for {modelType}
      </h1>

      {/* Toggle Supervised / Unsupervised */}
      <div className="space-x-4 mb-4">
        <button
          onClick={() => setIsUnsupervised(false)}
          className={`px-4 py-2 rounded ${!isUnsupervised ? "bg-blue-600 text-white" : "bg-gray-300"
            }`}
        >
          Supervised Learning
        </button>
        <button
          onClick={() => setIsUnsupervised(true)}
          className={`px-4 py-2 rounded ${isUnsupervised ? "bg-blue-600 text-white" : "bg-gray-300"
            }`}
        >
          Unsupervised Learning
        </button>
      </div>

      {/* Conditional render */}
      {!isUnsupervised ? (
        <>
          {/* Prediction form for supervised models */}
          <ModelPredictionForm
            modelType={modelType}
            onPredict={handlePrediction}
            targetColumn={targetColumn}
            setTargetColumn={setTargetColumn}
            selectedFeatureValues={selectedFeatureValues}
            setSelectedFeatureValues={setSelectedFeatureValues}
          />
          {/* Prediction results viewer */}
          {results && <ModelResults results={results} />}

          {/* Input list for features used in prediction */}
          <PredictFeatureInputList
            allFeatureOrder={allFeatureOrder}
            allItemsMap={allItemsMap}
            ordinalMap={ordinalMap}
            targetColumn={targetColumn}
            selectedFeatureValues={selectedFeatureValues}
            setSelectedFeatureValues={setSelectedFeatureValues}
            onTrainClick={() => {
              console.log("Training started...");
            }}
          />
        </>
      ) : (
        // Unsupervised Mode Component
        <ModelUnsupervise modelType={modelType} onPredict={handlePrediction} />
      )}
    </div>
  );
};

export default ModelPrediction;
