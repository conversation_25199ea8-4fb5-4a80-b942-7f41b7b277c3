import React, { useState } from "react";
import CleansingTypos from "./CleansingTypos";
import ApplyTable from "../ApplyTable";
import { useSelector } from "react-redux";

const Typo = () => {
  // State for toggle show apply table
  const [showTable, setShowTable] = useState(false);

  // State for set parameter
  const [resetParameter, setResetParameter] = useState(false);

  // Redux state for pull dataset
  const dataset = useSelector((state) => state.upload.dataset);

  // Show table when apply button is clicked
  const handleApply = () => {
    setShowTable(true);
  };

  // Close the table and reset the parameters when reset button is clicked
  const handleResetParameters = () => {
    setResetParameter(true);
    setShowTable(false);
  };

  return (
    <div>
      {/* Replace suggestion typo checkbox */}
      <h1 className="font-bold mb-4 text-xl">
        Check choice that you need to change:
      </h1>

      {/* Change typo manually */}
      <CleansingTypos
        resetParameter={resetParameter}
        setResetParameter={setResetParameter}
      />

      {/* apply and reset parameter button */}
      {/* <PERSON><PERSON> for applying the selected method and resetting the parameters */}
      <div className="flex flex-col items-center gap-5 p-5 w-full">
        <div className="flex gap-5">
          <button
            className="bg-[#a4c2f4] text-black py-4 px-6 text-lg rounded min-w-[250px] transition-colors hover:bg-[#8ab4f8] active:bg-[#7aa0e0]"
            onClick={handleApply}
          >
            Apply Typo Correction
          </button>
          <button
            className="bg-[#a4c2f4] text-black py-4 px-6 text-lg rounded min-w-[250px] transition-colors hover:bg-[#8ab4f8] active:bg-[#7aa0e0]"
            onClick={handleResetParameters}
          >
            Reset Parameters
          </button>
        </div>
      </div>

      {/* Table show apply result */}
      {/* Show the table if showTable is true */}
      {showTable && (
        <ApplyTable originalData={dataset} cleanedData={dataset} tab="Typo" />
      )}
    </div>
  );
};

export default Typo;
