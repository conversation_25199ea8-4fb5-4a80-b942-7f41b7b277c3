import React, { useState, useEffect } from "react";

const LogisticRegressionForm = ({ onParamsChange }) => {
  const [params, setParams] = useState({
    C: 1.0,
    penalty: "l2",
    solver: "lbfgs",
  });

  useEffect(() => {
    onParamsChange(params);
  }, [params, onParamsChange]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setParams((prevParams) => ({
      ...prevParams,
      [name]: value,
    }));
  };

  return (
    <div className="space-y-2 p-4 bg-white rounded shadow">
      <div>
        <label className="block text-sm font-medium">C (Regularization):</label>
        <input
          type="number"
          name="C"
          value={params.C}
          onChange={handleChange}
          className="mt-1 p-2 border rounded w-full"
          step="0.1"
        />
      </div>

      <div>
        <label className="block text-sm font-medium">Penalty:</label>
        <select
          name="penalty"
          value={params.penalty}
          onChange={handleChange}
          className="mt-1 p-2 border rounded w-full"
        >
          <option value="l1">L1</option>
          <option value="l2">L2</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium">Solver:</label>
        <select
          name="solver"
          value={params.solver}
          onChange={handleChange}
          className="mt-1 p-2 border rounded w-full"
        >
          <option value="lbfgs">lbfgs</option>
          <option value="liblinear">liblinear</option>
          <option value="sag">sag</option>
          <option value="saga">saga</option>
        </select>
      </div>
    </div>
  );
};

export default LogisticRegressionForm;
