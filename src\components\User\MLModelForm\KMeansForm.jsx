import React, { useState, useEffect } from "react";

/**
 * @typedef {Object} KMeansParams
 * @property {number} n_clusters - จำนวนของกลุ่มที่จะสร้าง (clusters)
 * @property {"k-means++" | "random"} init - วิธีการกำหนดค่าเริ่มต้นสำหรับ centroid
 * @property {number} max_iter - จำนวนรอบสูงสุดในการทำ iteration
 */

/**
 * KMeansForm เป็น React component สำหรับรับพารามิเตอร์ที่ใช้ในอัลกอริทึม K-Means
 * ผู้ใช้สามารถกำหนดจำนวน cluster, วิธีการเริ่มต้น, และจำนวนรอบสูงสุดในการเรียนรู้
 *
 * @component
 * @param {Object} props - Properties ที่ส่งมาจาก parent component
 * @param {(params: KMeansParams) => void} props.onParamsChange - Callback ที่เรียกเมื่อพารามิเตอร์เปลี่ยนแปลง
 * @returns {JSX.Element} React component สำหรับฟอร์มรับค่าพารามิเตอร์ของ K-Means
 */

const KMeansForm = ({ onParamsChange }) => {
    /** @type {[KMeansParams, Function]} */
    const [params, setParams] = useState({
        n_clusters: 8,
        init: "k-means++",
        max_iter: 300,
    });

    // ส่งค่าพารามิเตอร์กลับไปยัง parent เมื่อค่าถูกเปลี่ยน
    useEffect(() => {
        onParamsChange(params);
    }, [params, onParamsChange]);

    /**
 * จัดการการเปลี่ยนแปลงค่าจาก input field
 *
 * @param {React.ChangeEvent<HTMLInputElement | HTMLSelectElement>} e - อีเวนต์จาก input/select
 */
    const handleChange = (e) => {
        const { name, value } = e.target;
        setParams((prevParams) => ({
            ...prevParams,
            [name]: name === "init" ? value : Number(value),
        }));
    };

    return (
        <div className="space-y-2 p-4 bg-white rounded shadow">
            {/* Input: จำนวนของ cluster */}
            <div>
                <label className="block text-sm font-medium">n_clusters:</label>
                <input
                    type="number"
                    name="n_clusters"
                    value={params.n_clusters}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                    min="1"
                />
            </div>

            {/* Select: วิธีการเริ่มต้น */}
            <div>
                <label className="block text-sm font-medium">init:</label>
                <select
                    name="init"
                    value={params.init}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                >
                    <option value="k-means++">k-means++</option>
                    <option value="random">random</option>
                </select>
            </div>

            {/* Input: จำนวนรอบสูงสุด */}
            <div>
                <label className="block text-sm font-medium">max_iter:</label>
                <input
                    type="number"
                    name="max_iter"
                    value={params.max_iter}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                />
            </div>
        </div>
    );
};

export default KMeansForm;
