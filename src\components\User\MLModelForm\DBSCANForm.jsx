import React, { useState, useEffect } from "react";

const DBSCANForm = ({ onParamsChange }) => {
    const [params, setParams] = useState({
        eps: 0.5,
        min_samples: 5,
        metric: "euclidean",
    });

    useEffect(() => {
        onParamsChange(params);
    }, [params, onParamsChange]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setParams((prevParams) => ({
            ...prevParams,
            [name]: name === "metric" ? value : Number(value),
        }));
    };

    return (
        <div className="space-y-4 p-4 bg-white rounded shadow">
            <div>
                <label className="block text-sm font-medium">eps (epsilon):</label>
                <input
                    type="number"
                    step="0.1"
                    name="eps"
                    value={params.eps}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                    min="0.1"
                />
            </div>

            <div>
                <label className="block text-sm font-medium">min_samples:</label>
                <input
                    type="number"
                    name="min_samples"
                    value={params.min_samples}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                    min="1"
                />
            </div>

            <div>
                <label className="block text-sm font-medium">Distance Metric:</label>
                <select
                    name="metric"
                    value={params.metric}
                    onChange={handleChange}
                    className="mt-1 p-2 border rounded w-full"
                >
                    <option value="euclidean">Euclidean</option>
                    <option value="manhattan">Manhattan</option>
                    <option value="chebyshev">Chebyshev</option>
                </select>
            </div>
        </div>
    );
};

export default DBSCANForm;
