import React, { useState, useEffect, useMemo, useCallback } from "react";
import { useSelector } from "react-redux"; // React hook และ Redux hook  state
import Dropdown from "../dropdown";
import CustomSlider from "../CustomSlider";
import ApplyTable from "../ApplyTable";

// ==================== Assign option of dropdown in duplicate handling ======================
// For 'Aggregate Duplicates' option
const aggregationOptions = ["sum", "mean", "median", "count", "mode"];
// For 'Use Similarity or Clustering' option
const similarityClusteringMethods = [
  "Levenshtein Distance",
  "Jaccard Similarity",
  "Cosine Similarity",
];
// For 'Handle Duplicates in Text' option
const textSimilarityMethods = ["Fuzzy Matching", "Cosine Similarity"];

// Helper function to remove the "-number" suffix from a feature name
// /../ : These slashes delimit the beginning and end of the regular expression.
// - : This is a literal hyphen character. The pattern will only match if it finds a hyphen.
// \d : This is a "character class" that matches any single digit (0-9).
// + : This is a "quantifier." It means "match the preceding character one or more times." So, \d+ will match one or more consecutive digits.
// $ : This is an "anchor." It asserts the position at the very end of the string.
// summary : Match a hyphen (-) followed by one or more digits (\d+), but only if this entire sequence is at the end of the string ($).
const cleanUniqueId = (str) => str.replace(/-\d+$/, "");

/**
 * Duplicate is a React component that provides multiple strategies to detect
 * and handle duplicate data. These include:
 *
 * - Removing exact duplicates.
 * - Aggregating duplicate entries using various aggregation functions.
 * - Detecting similarity using metrics like Levenshtein or Jaccard.
 * - Text-based duplicate handling using fuzzy matching or cosine similarity.
 *
 * @component
 * @returns {JSX.Element} Rendered UI for duplicate handling options.
 */
const Duplicate = () => {
  // Redux state for pull dataset
  const dataset = useSelector((state) => state.upload.dataset);

  // Redux features from uploaded dataset
  const features = useSelector((s) => s.upload.features) || {
    numerical: { items: [] },
    nominal: { items: [] },
    ordinal: { items: [] },
  };

  //===================== Classify feature data type from Redux state =====================
  // All feature data types
  /*
  allFeatures = [
    {
      disabled: false,
      feature: "Cloud3pm",
      id: "Cloud3pm-16",
      uniqueValue:[1, 3, 5, 6]
    },
  ]
  */
  const allFeatures = [
    ...features.numerical.items,
    ...features.nominal.items,
    ...features.ordinal.items,
  ];
  // Nominal + ordinal features
  const nominalOrdinalItems = [
    // เฉพาะ nominal + ordinal features
    ...features.nominal.items,
    ...features.ordinal.items,
  ];

  // ========================= Create a map of all feature by id for fast lookup ================================
  /*
    allFeaturesMap = {
      Cloud3pm-16: {
        disabled: false,
        feature: "Cloud3pm",
        id: "Cloud3pm-16",
        uniqueValue:[1, 3, 5, 6]
      },
    }
  */
  const allFeaturesMap = useMemo(
    () => Object.fromEntries(allFeatures.map((i) => [i.id, i])),
    [allFeatures]
  );

  const nominalOrdinalItemsMap = useMemo(
    () => Object.fromEntries(nominalOrdinalItems.map((i) => [i.id, i])),
    [nominalOrdinalItems]
  );

  // =================== Extract original feature order for show feature in order ===========
  const allFeatureOrder = useMemo(
    () => allFeatures.map((i) => i.id),
    [allFeatures]
  );
  const nominalOrdinalFeatureOrder = useMemo(
    () => nominalOrdinalItems.map((i) => i.id),
    [nominalOrdinalItems]
  );

  // ================================ State for 'Select Duplicate Handling Option' option ==============================
  const [selectedDuplicateOption, setSelectedDuplicateOption] =
    useState("Remove Duplicates");

  // ================================ State for 'Remove Duplicates' option ==============================
  const [removeDuplicatesFeatures, setRemoveDuplicatesFeatures] = useState([]);
  const [duplicateKeepOption, setDuplicateKeepOption] = useState("first");

  // ================================ State for 'Aggregate Duplicates' option ==============================

  // Function to get aggregation options for each feature - memoized
  const getAggregationOptionsForFeature = useCallback(
    (featureId) => {
      if (features.numerical?.items.find((item) => item.id === featureId)) {
        return aggregationOptions; // all options
      } else if (
        features.nominal?.items.find((item) => item.id === featureId)
      ) {
        return ["count", "mode"];
      } else if (
        features.ordinal?.items.find((item) => item.id === featureId)
      ) {
        return ["median", "mode", "count"];
      } else {
        return aggregationOptions; // default fallback
      }
    },
    [
      features.numerical?.items,
      features.nominal?.items,
      features.ordinal?.items,
    ]
  );

  const [groupByFeatures, setGroupByFeatures] = useState([]);
  const [aggregationSelections, setAggregationSelections] = useState({});

  // Compute initial aggregate duplicate options for each features by defaults - memoized to prevent unnecessary recalculations
  const computeInitialAggregationSelections = useCallback(() => {
    const initial = {};
    const aggregateFeatures = allFeatureOrder.filter(
      (id) => !groupByFeatures.includes(id)
    );
    aggregateFeatures.forEach((featureId) => {
      const availableOptions = getAggregationOptionsForFeature(featureId);

      // Only use existing selection if it's valid for this feature
      if (
        aggregationSelections[featureId] &&
        availableOptions.includes(aggregationSelections[featureId])
      ) {
        initial[featureId] = aggregationSelections[featureId];
      } else if (
        features.numerical?.items.find((item) => item.id === featureId)
      ) {
        initial[featureId] = "mean";
      } else if (
        features.nominal?.items.find((item) => item.id === featureId)
      ) {
        initial[featureId] = "mode";
      } else if (
        features.ordinal?.items.find((item) => item.id === featureId)
      ) {
        initial[featureId] = "median";
      } else {
        initial[featureId] = "";
      }
    });
    return initial;
  }, [
    groupByFeatures,
    aggregationSelections,
    features.numerical?.items,
    features.nominal?.items,
    features.ordinal?.items,
  ]);

  // Update aggregation selections when groupByFeatures change
  useEffect(() => {
    const newAggregationSelections = computeInitialAggregationSelections();
    setAggregationSelections(newAggregationSelections);
  }, [groupByFeatures]);

  // ================================ State for 'Use Similarity or Clustering' option ==============================

  const [selectedSimilarityFeatures, setSelectedSimilarityFeatures] = useState(
    []
  );

  const [
    selectedSimilarityClusteringMethod,
    setSelectedSimilarityClusteringMethod,
  ] = useState("");
  const [similarityClusteringThreshold, setSimilarityClusteringThreshold] =
    useState(0);

  // ================================ State for 'Handle Duplicates in Text' option =================================
  // Text Similarity feature state
  const [selectedTextDuplicateFeatures, setSelectedTextDuplicateFeatures] =
    useState([]);
  const [selectedTextSimilarityMethod, setSelectedTextSimilarityMethod] =
    useState("");
  const [textSimilarityThreshold, setTextSimilarityThreshold] = useState(0);

  // ======================= State for apply duplicate and reset parameter ==================================
  // State for toggle show apply table
  const [showTable, setShowTable] = useState(false);

  // ======================= Function for select handle duplicates option ==================================
  /**
   * Updates the selected duplicate option in the Redux store.
   * @param {string} value
   */
  const handleOptionChange = (value) => {
    setSelectedDuplicateOption(value);
  };

  // ======================= Function for 'Remove Duplicates' option ===================================
  /**
   * Toggles a feature in the list of selected features for duplicate identification.
   * @param {string} featureId
   */
  const handleRemoveDuplicatesFeatureToggle = (featureName) => {
    const updated = removeDuplicatesFeatures.includes(featureName)
      ? removeDuplicatesFeatures.filter((id) => id !== featureName)
      : [...removeDuplicatesFeatures, featureName];
    setRemoveDuplicatesFeatures(updated);
  };

  // ======================== Function for 'Aggregate Duplicates' option ======================================
  const handleGroupByChange = (featureId) => {
    const updated = groupByFeatures.includes(featureId)
      ? groupByFeatures.filter((featureId) => featureId !== featureId)
      : [...groupByFeatures, featureId];
    setGroupByFeatures(updated);
  };

  // ========================== Function for 'Use Similarity or Clustering' option ==============================
  const handleSimilarityClusteringFeatureChange = (id) => {
    setSelectedSimilarityFeatures((prev) =>
      prev.includes(id) ? prev.filter((fid) => fid !== id) : [...prev, id]
    );
  };

  const handleSimilarityMethodChange = (value) =>
    setSelectedSimilarityClusteringMethod(value);

  // =========================== Function for 'Handle Duplicates in Text' option ============================
  const handleTextSimilarityMethodChange = (value) => {
    setSelectedTextSimilarityMethod(value);
  };

  // =========================== Function for apply duplicate and reset parameter ============================
  // Show table when apply button is clicked
  const handleApply = () => {
    setShowTable(true);
  };

  // Close the table and reset the parameters when reset button is clicked
  const handleResetParameters = () => {
    setSelectedDuplicateOption("Remove Duplicates");
    setRemoveDuplicatesFeatures([]);
    setDuplicateKeepOption("first");
    setGroupByFeatures([]);
    setAggregationSelections({});
    setSelectedSimilarityFeatures([]);
    setSelectedSimilarityClusteringMethod("");
    setSimilarityClusteringThreshold(0);
    setSelectedTextDuplicateFeatures([]);
    setSelectedTextSimilarityMethod("");
    setTextSimilarityThreshold(0);
    setShowTable(false);
  };

  const filteredSimilarityMetrics = useMemo(() => {
    if (selectedSimilarityFeatures.length === 0) {
      return similarityClusteringMethods; // default fallback
    }

    const hasNumericalFeature = selectedSimilarityFeatures.some((id) =>
      features.numerical?.items.find((item) => item.id === id)
    );
    const hasNominalFeature = selectedSimilarityFeatures.some((id) =>
      features.nominal?.items.find((item) => item.id === id)
    );
    const hasOrdinalFeature = selectedSimilarityFeatures.some((id) =>
      features.ordinal?.items.find((item) => item.id === id)
    );

    if (hasNumericalFeature) {
      return ["Cosine Similarity"];
    } else if (hasOrdinalFeature) {
      return ["Levenshtein Distance", "Cosine Similarity"];
    } else if (hasNominalFeature) {
      return [
        "Jaccard Similarity",
        "Levenshtein Distance",
        "Cosine Similarity",
      ];
    } else {
      return similarityClusteringMethods;
    }
  }, [selectedSimilarityFeatures, features]);

  useEffect(() => {
    const hasNumericalFeature = selectedSimilarityFeatures.some((id) =>
      features.numerical?.items.find((item) => item.id === id)
    );
    const hasNominalFeature = selectedSimilarityFeatures.some((id) =>
      features.nominal?.items.find((item) => item.id === id)
    );
    const hasOrdinalFeature = selectedSimilarityFeatures.some((id) =>
      features.ordinal?.items.find((item) => item.id === id)
    );

    if (hasNumericalFeature) {
      setSelectedSimilarityClusteringMethod("Cosine Similarity");
    } else if (hasOrdinalFeature) {
      setSelectedSimilarityClusteringMethod("Levenshtein Distance");
    } else {
      setSelectedSimilarityClusteringMethod("Jaccard Similarity");
    }
  }, [selectedSimilarityFeatures, features]);

  useEffect(() => {
    console.log("removeDuplicatesFeatures: ", removeDuplicatesFeatures);
    console.log("groupByFeatures: ", groupByFeatures);
    console.log("aggregationSelections: ", aggregationSelections);
    console.log("selectedSimilarityFeatures: ", selectedSimilarityFeatures);
    console.log(
      "selectedTextDuplicateFeatures: ",
      selectedTextDuplicateFeatures
    );
  }, [
    removeDuplicatesFeatures,
    groupByFeatures,
    aggregationSelections,
    selectedSimilarityFeatures,
    selectedTextDuplicateFeatures,
  ]);

  /**
   * Renders the UI depending on the selected duplicate handling mode.
   * @returns {JSX.Element|null}
   */
  const renderContent = () => {
    switch (selectedDuplicateOption) {
      case "Remove Duplicates":
        return (
          <div>
            <h1 className="font-bold mb-4 text-xl">
              Select features to identify duplicates :
            </h1>
            <div className="flex flex-col">
              {allFeatureOrder
                .filter(
                  (id) => !allFeaturesMap[id].disabled // feature is not disabled(disabled = false)
                )
                .map((id) => {
                  const item = allFeaturesMap[id];
                  return (
                    <div key={item.id} className="flex items-center mb-2">
                      <input
                        type="checkbox"
                        id={item.id}
                        className="mr-2"
                        checked={removeDuplicatesFeatures.includes(
                          item.feature
                        )}
                        onChange={() =>
                          handleRemoveDuplicatesFeatureToggle(item.feature)
                        }
                      />
                      <span>{item.feature}</span>
                    </div>
                  );
                })}
            </div>

            <h2 className="font-semibold text-lg mt-6 mb-2">
              Which duplicates to keep?
            </h2>
            <div className="flex flex-col space-y-2">
              {["first", "last", "remove"].map((option) => (
                <label key={option} className="flex items-center">
                  <input
                    type="radio"
                    name="duplicateKeepOption"
                    value={option}
                    checked={duplicateKeepOption === option}
                    onChange={(e) => setDuplicateKeepOption(e.target.value)}
                    className="mr-2"
                  />
                  {option === "first"
                    ? "Keep First Occurrence"
                    : option === "last"
                    ? "Keep Last Occurrence"
                    : "Remove All Duplicates"}
                </label>
              ))}
            </div>
          </div>
        );

      case "Aggregate Duplicates":
        // features that are not in groupByItems and available in allFeaturesMap
        const aggregateFeatures = allFeatureOrder.filter(
          (id) => !groupByFeatures.includes(id)
        );

        return (
          <div>
            <h1 className="font-bold mb-4 text-xl">
              Select features to group by:
            </h1>
            <div className="flex flex-col">
              {allFeatureOrder
                .filter(
                  (id) => !allFeaturesMap[id].disabled // feature is not disabled(disabled = false)
                )
                .map((id) => {
                  const item = allFeaturesMap[id];
                  return (
                    <div key={item.id} className="flex items-center mb-2">
                      <input
                        type="checkbox"
                        id={item.id}
                        className="mr-2"
                        checked={groupByFeatures.includes(item.id)}
                        onChange={() => handleGroupByChange(id)}
                      />
                      <span>{item.feature}</span>
                    </div>
                  );
                })}
            </div>

            <h2 className="font-bold mt-6 mb-2 text-lg">
              Select features to aggregate and their function:
            </h2>
            <div className="flex flex-col space-y-4 max-w-md">
              {aggregateFeatures.map((featureId) => {
                const featureName =
                  allFeaturesMap[featureId]?.feature || featureId;
                const availableOptions =
                  getAggregationOptionsForFeature(featureId);

                return (
                  <div key={featureId} className="flex items-center space-x-4">
                    <span className="w-32 font-medium capitalize">
                      {featureName}
                    </span>
                    <Dropdown
                      options={availableOptions}
                      value={aggregationSelections[featureId] || ""}
                      setValue={(value) => {
                        setAggregationSelections((prev) => ({
                          ...prev,
                          [featureId]: value,
                        }));
                      }}
                    />
                  </div>
                );
              })}
            </div>
          </div>
        );
      case "Use Similarity or Clustering":
        return (
          <div>
            <h1 className="font-bold mb-4 text-xl">
              Select features for similarity detection :
            </h1>
            <div className="flex flex-col">
              {allFeatureOrder
                .filter((id) => !allFeaturesMap[id].disabled)
                .map((id) => {
                  const item = allFeaturesMap[id];
                  return (
                    <div key={item.id} className="flex items-center mb-2">
                      <input
                        type="checkbox"
                        id={item.id}
                        className="mr-2"
                        checked={selectedSimilarityFeatures.includes(id)}
                        onChange={() =>
                          handleSimilarityClusteringFeatureChange(id)
                        }
                      />
                      <span>{item.feature}</span>
                    </div>
                  );
                })}
            </div>

            <Dropdown
              options={filteredSimilarityMetrics}
              tell="Select similarity metric:"
              placeholder="Select similarity metric"
              value={selectedSimilarityClusteringMethod}
              setValue={handleSimilarityMethodChange}
            />

            <h2 className="font-bold mt-6 mb-2 text-lg">
              Similarity threshold:
            </h2>
            <CustomSlider
              min={0.0}
              max={1.0}
              step={0.1}
              defaultValue={similarityClusteringThreshold}
              onChange={(value) => setSimilarityClusteringThreshold(value)}
            />
          </div>
        );

      case "Handle Duplicates in Text":
        return (
          <div>
            <h1 className="font-bold mb-4 text-xl">
              Select Categorical feature to analyze :
            </h1>
            {nominalOrdinalFeatureOrder
              .filter((id) => !nominalOrdinalItemsMap[id].disabled)
              .map((id) => {
                const item = nominalOrdinalItemsMap[id];
                return (
                  <div key={item.id} className="flex items-center mb-2">
                    <input
                      type="checkbox"
                      name="categoricalFeature"
                      id={item.id}
                      className="mr-2"
                      checked={selectedTextDuplicateFeatures.includes(id)}
                      onChange={(e) => {
                        let updated;
                        if (e.target.checked) {
                          updated = [...selectedTextDuplicateFeatures, id];
                        } else {
                          updated = selectedTextDuplicateFeatures.filter(
                            (fid) => fid !== id
                          );
                        }

                        setSelectedTextDuplicateFeatures(updated);
                      }}
                    />
                    <span>{item.feature}</span>
                  </div>
                );
              })}

            <h2 className="font-bold mt-6 mb-2 text-lg">
              Select text similarity method:
            </h2>
            <Dropdown
              options={textSimilarityMethods}
              value={selectedTextSimilarityMethod}
              setValue={handleTextSimilarityMethodChange}
              searchPlaceholder="Select method"
            />

            <h2 className="font-bold mt-6 mb-2 text-lg">
              Text similarity threshold:
            </h2>
            <CustomSlider
              min={0.0}
              max={1.0}
              step={0.1}
              defaultValue={textSimilarityThreshold}
              onChange={(value) => setTextSimilarityThreshold(value)}
            />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="p-4">
      <Dropdown
        options={[
          "Remove Duplicates",
          "Aggregate Duplicates",
          "Use Similarity or Clustering",
          "Handle Duplicates in Text",
        ]}
        tell="Select duplicate handling method:"
        placeholder="Select duplicate handling method"
        value={selectedDuplicateOption}
        setValue={handleOptionChange}
        searchPlaceholder="Select duplicate handling method"
      />
      <div className="mt-6">{renderContent()}</div>
      {/* apply and reset parameter button */}
      {/* Button for applying the selected method and resetting the parameters */}
      <div className="flex flex-col items-center gap-5 p-5 w-full">
        <div className="flex gap-5">
          <button
            className="bg-[#a4c2f4] text-black py-4 px-6 text-lg rounded min-w-[250px] transition-colors hover:bg-[#8ab4f8] active:bg-[#7aa0e0]"
            onClick={handleApply}
          >
            Apply Typo Correction
          </button>
          <button
            className="bg-[#a4c2f4] text-black py-4 px-6 text-lg rounded min-w-[250px] transition-colors hover:bg-[#8ab4f8] active:bg-[#7aa0e0]"
            onClick={handleResetParameters}
          >
            Reset Parameters
          </button>
        </div>
      </div>

      {/* Table show apply result */}
      {/* Show the table if showTable is true */}
      {showTable && (
        <ApplyTable
          originalData={dataset}
          cleanedData={dataset}
          tab="Duplicate"
        />
      )}
    </div>
  );
};

export default Duplicate;
