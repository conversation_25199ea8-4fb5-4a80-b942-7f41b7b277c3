import React, { useState, useEffect } from "react";
import Plot from "react-plotly.js";

const ApplyButton = ({ setShowTable, currentPage }) => {
    const [showClusteringPlot, setShowClusteringPlot] = useState(false);
    const [plotData, setPlotData] = useState(null);
    const [dimension, setDimension] = useState(2);

    const axisOptions2D = ["x", "y"];
    const axisOptions3D = ["x", "y", "z"];

    const [xAxis, setXAxis] = useState("x");
    const [yAxis, setYAxis] = useState("y");
    const [zAxis, setZAxis] = useState("z");

    const dummyReducedData = {
        x: [1, 2, 3, 4, 5],
        y: [10, 15, 13, 17, 14],
        z: [5, 6, 7, 8, 9],
        labels: ["A", "B", "C", "D", "E"],
    };

    useEffect(() => {
        if (dimension === 2) {
          setXAxis("x");
          setYAxis("y");
          setZAxis(""); // clear z axis when 2D
        } else {
          setXAxis("x");
          setYAxis("y");
          setZAxis("z");
        }
    }, [dimension]);

    const handleApply = () => {
        if (currentPage === "dimensional-reduction") {
          setPlotData(dummyReducedData);
          setShowClusteringPlot(true);
        } else {
          setShowTable(true);
        }
      };

      const handleResetParameters = () => {
        setShowTable(false);
        setShowClusteringPlot(false);
      };

      return (
        <div className="flex flex-col items-center gap-5 p-5 w-full">
          <div className="flex gap-5">
            <button
              className="bg-[#a4c2f4] text-black py-4 px-6 text-lg rounded min-w-[250px] transition-colors hover:bg-[#8ab4f8] active:bg-[#7aa0e0]"
              onClick={handleApply}
            >
              Apply {currentPage}
            </button>
            <button
              className="bg-[#a4c2f4] text-black py-4 px-6 text-lg rounded min-w-[250px] transition-colors hover:bg-[#8ab4f8] active:bg-[#7aa0e0]"
              onClick={handleResetParameters}
            >
              Reset {currentPage} Parameters
            </button>
          </div>
    
          {showClusteringPlot && currentPage === "dimensional-reduction" && plotData && (
            <div className="mt-6 w-full max-w-6xl flex items-center justify-center gap-10 p-6 rounded-lg">
              {/* Controls */}
              <div className="flex flex-col gap-6">
                {/* Dimension Selector */}
                <div className="flex flex-col items-start">
                  <label className="block mb-2 font-semibold">dimension</label>
                  {/* ให้เลือก 2D หรือ 3D */}
                  <select
                    value={dimension}
                    onChange={(e) => setDimension(Number(e.target.value))}
                    className="bg-white rounded-full px-6 py-3 w-36 border border-gray-300 shadow"
                  >
                    <option value={2}>2D</option>
                    <option value={3}>3D</option>
                  </select>
                </div>
                {/* X-axis */}
                <div>
                  <label className="block mb-1 font-semibold">x-axis</label>
                  <select
                    value={xAxis}
                    onChange={(e) => setXAxis(e.target.value)}
                    className="bg-white rounded-full px-4 py-2 w-36 border border-gray-300 shadow"
                  >
                    {(dimension === 2 ? axisOptions2D : axisOptions3D).map((axis) => (
                      <option key={axis} value={axis}>
                        {axis}
                      </option>
                    ))}
                  </select>
                </div>
                {/* Y-axis */}
                <div>
                  <label className="block mb-1 font-semibold">y-axis</label>
                  <select
                    value={yAxis}
                    onChange={(e) => setYAxis(e.target.value)}
                    className="bg-white rounded-full px-4 py-2 w-36 border border-gray-300 shadow"
                  >
                    {(dimension === 2 ? axisOptions2D : axisOptions3D).map((axis) => (
                      <option key={axis} value={axis}>
                        {axis}
                      </option>
                    ))}
                  </select>
                </div>
                {/* Z-axis */}
                {dimension === 3 && (
                  <div>
                    <label className="block mb-1 font-semibold">z-axis</label>
                    <select
                      value={zAxis}
                      onChange={(e) => setZAxis(e.target.value)}
                      className="bg-white rounded-full px-4 py-2 w-36 border border-gray-300 shadow"
                    >
                      {axisOptions3D.map((axis) => (
                        <option key={axis} value={axis}>
                          {axis}
                        </option>
                      ))}
                    </select>
                  </div>
                )}
              </div>
    
              {/* Plot */}
              <div>
                {dimension === 2 ? (
                  <Plot
                    data={[
                      {
                        x: plotData[xAxis],
                        y: plotData[yAxis],
                        type: "scatter",
                        mode: "markers+text",
                        marker: { color: "blue", size: 10 },
                        text: plotData.labels,
                        textposition: "top center",
                      },
                    ]}
                    layout={{ width: 600, height: 400, title: "2D Dimensional Reduction Plot" }}
                  />
                ) : (
                  <Plot
                    data={[
                      {
                        x: plotData[xAxis],
                        y: plotData[yAxis],
                        z: plotData[zAxis],
                        type: "scatter3d",
                        mode: "markers+text",
                        marker: { size: 5, color: "red" },
                        text: plotData.labels,
                        textposition: "top center",
                      },
                    ]}
                    layout={{ width: 600, height: 400, title: "3D Dimensional Reduction Plot" }}
                  />
                )}
              </div>
            </div>
          )}
        </div>
      );
    };
    
    export default ApplyButton;
