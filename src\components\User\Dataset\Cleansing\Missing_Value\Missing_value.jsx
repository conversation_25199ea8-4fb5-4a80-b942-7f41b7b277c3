import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import Dropdown from "../dropdown";
import CustomSlider from "../CustomSlider";
import ConstantImputation from "./ConstantImputation";
import ApplyTable from "../ApplyTable";

// Missing value handling method
const options = ["Drop Rows", "Drop Columns", "Impute missing value"];

// 'Impute missing value' method's option of categorical feature
const numericalImputeOptions = [
  "Means Imputation",
  "Median Imputation",
  "Constant Imputation",
];

// 'Impute missing value' method's option of numerical feature
const CategoricalImputeOptions = [
  "Most Frequent Imputation",
  "Constant Imputation",
];

// Helper function to remove the "-number" suffix from a feature name
/*
      How it works:

      .replace(...): This is a string method in JavaScript. It's used to find a pattern within a string and replace it with something else.

      /-\d+$/: This is a regular expression (regex). Let's break down the regex:

      /-: Matches a literal hyphen character. The hyphen doesn't have special meaning in regex unless it's within a character set [] or used for ranges.
      \d: Matches any digit (equivalent to [0-9]).
      +: Quantifier, meaning "one or more" of the preceding element. So, \d+ means one or more digits.
      $: Anchor, meaning "end of the string." This ensures that the pattern (-\d+) is only matched if it occurs at the very end of the featureId string.
      '' (empty string): This is the replacement value. Whatever matches the regular expression /-\d+$/ will be replaced with an empty string, effectively removing it from the featureId.
    */
const cleanUniqueId = (str) => str.replace(/-\d+$/, "");

/**
 * Missing_value component for handling missing values in a dataset.
 *
 * This component provides options to drop rows, drop columns, or impute missing values
 * using various methods. It interacts with the Redux store to manage state and updates.
 *
 * @component
 * @returns {JSX.Element} The rendered UI for missing value handling.
 */
const Missing_value = () => {
  /**
   * Local state for missing value handling.
   * @var {string} selectedMissingValueOption - Selected option for handling missing values ("Drop Rows", "Drop Columns", "Impute missing value").
   * @var {Array<string>} dropRowFeatures - Features selected for dropping rows.
   * @var {Array<string>} dropColumnFeatures - Features selected for dropping columns.
   * @var {Array<{method: string, features: Array<string>}>} numericalMissingValueMethods - Methods for handling numerical missing values.
   * @var {Array<{method: string, features: Array<string>}>} categoricalMissingValueMethods - Methods for handling categorical missing values.
   * @var {boolean} showDropRowsThreshold - Whether to show the threshold slider for dropping rows.
   * @var {number} dropRowsThresholdValue - Threshold value for dropping rows.
   * @var {boolean} showDropColumnsThreshold - Whether to show the threshold slider for dropping columns.
   * @var {number} dropColumnsThresholdValue - Threshold value for dropping columns.
   * @var {boolean} showTable - Whether to show the table for applying missing value handling.
   */

  const [selectedMissingValueOption, setSelectedMissingValueOption] =
    useState("");
  const [dropRowFeatures, setDropRowFeatures] = useState([]);
  const [dropColumnFeatures, setDropColumnFeatures] = useState([]);
  const [numericalMissingValueMethods, setNumericalMissingValueMethods] =
    useState([]);
  const [categoricalMissingValueMethods, setCategoricalMissingValueMethods] =
    useState([]);
  const [showDropRowsThreshold, setShowDropRowsThreshold] = useState(false);
  const [dropRowsThresholdValue, setDropRowsThresholdValue] = useState(50);
  const [showDropColumnsThreshold, setShowDropColumnsThreshold] =
    useState(false);
  const [dropColumnsThresholdValue, setDropColumnsThresholdValue] =
    useState(50);
  const [showTable, setShowTable] = useState(false);

  //============================= fetch dataset from Redux state ===============================
  const dataset = useSelector((state) => state.upload.dataset);

  //============================= fetch feature data type from Redux state ===============================
  /*
  features = {
    allFeatureOrder: ['MinTemp-0', 'MaxTemp-1'],
    unassigned: {},
    numerical: {},
    nominal: {},
    ordinal: {}
  }
  */
  const features = useSelector((state) => state.upload.features);

  //=========================== classify feature data type from Redux state ====================
  // Use only numerical features
  const numericalItems = features.numerical.items;
  // Use only nominal and ordinal features
  const categoricalItems = [
    ...features.nominal.items,
    ...features.ordinal.items,
  ];

  // ========================= Create a map of all feature by id for fast lookup ================================
  const allItemsMap = {};
  [
    ...features.unassigned.items,
    ...features.numerical.items,
    ...features.nominal.items,
    ...features.ordinal.items,
  ].forEach((item) => {
    allItemsMap[item.id] = item;
  });

  const allNumericalItemsMap = {};
  numericalItems.forEach((item) => {
    allNumericalItemsMap[item.id] = item;
  });

  const allCategoricalItemsMap = {};
  categoricalItems.forEach((item) => {
    allCategoricalItemsMap[item.id] = item;
  });

  // =================== Extract original feature order for show feature in order ===========
  const allFeatureOrder = features.allFeatureOrder || Object.keys(allItemsMap);
  const allNumericalFeatureOrder = numericalItems.map((item) => item.id);
  const allCategoricalFeatureOrder = categoricalItems.map((item) => item.id);

  /**
   * Handles the change of 'numericalMissingValueMethods' and 'categoricalMissingValueMethods'. And initializes default methods if needed
   * @param {string} value - The selected missing value handling option ("Drop Rows", "Drop Columns", or "Impute missing value")
   * @description When "Impute missing value" is selected:
   * - If both numerical and categorical methods are empty, add both with empty method({ method: "", features: [] })
   * - If only numerical methods are empty, add numerical method with empty method({ method: "", features: [] })
   * - If only categorical methods are empty, add categorical method with empty method({ method: "", features: [] })
   * - Otherwise, just updates the selected option
   */
  const handleOptionChange = (value) => {
    if (
      value === "Impute missing value" &&
      numericalMissingValueMethods.length === 0 &&
      categoricalMissingValueMethods.length === 0
    ) {
      setSelectedMissingValueOption(value);
      setNumericalMissingValueMethods([{ method: "", features: [] }]);
      setCategoricalMissingValueMethods([{ method: "", features: [] }]);
    } else if (
      value === "Impute missing value" &&
      numericalMissingValueMethods.length === 0
    ) {
      setSelectedMissingValueOption(value);
      setNumericalMissingValueMethods([{ method: "", features: [] }]);
    } else if (
      value === "Impute missing value" &&
      categoricalMissingValueMethods.length === 0
    ) {
      setSelectedMissingValueOption(value);
      setCategoricalMissingValueMethods([{ method: "", features: [] }]);
    } else {
      setSelectedMissingValueOption(value);
    }
  };

  /**
   * Handles the toggle of show drop rows threshold slider and updates the Local state
   * @param {boolean} value - The new toggle value of the drop rows threshold slider
   */
  const handleShowDropRowsThresholdChange = (value) => {
    setShowDropRowsThreshold(value);
  };

  /**
   * Handles value of drop rows threshold slider and updates the Local state
   * @param {boolean} value - The new value of the drop rows threshold slider
   */
  const handleDropRowsThresholdValueChange = (value) => {
    setDropRowsThresholdValue(value);
  };

  /**
   * Handles the toggle of show drop columns threshold slider and updates the Local state
   * @param {boolean} value - The new toggle value of the drop columns threshold slider
   */
  const handleShowDropColumnsThresholdChange = (value) => {
    setShowDropColumnsThreshold(value);
  };

  /**
   * Handles value of drop columns threshold slider and updates the Local state
   * @param {boolean} value - The new value of the drop columns threshold slider
   */
  const handleDropColumnsThresholdValueChange = (value) => {
    setDropColumnsThresholdValue(value);
  };

  // Add numerical method
  const handleAddNumericalMethod = () => {
    setNumericalMissingValueMethods([
      ...numericalMissingValueMethods,
      { method: "", features: [] },
    ]);
  };

  // Add categorical method
  const handleAddCategoricalMethod = () => {
    setCategoricalMissingValueMethods([
      ...categoricalMissingValueMethods,
      { method: "", features: [] },
    ]);
  };

  /**
   * Change numerical method for a specific index
   * @param {string} value - Method's name
   * @param {number} idx - index of method
   */
  const handleNumericalOptionChange = (value, idx) => {
    const prevMethod = numericalMissingValueMethods[idx]?.method;
    const isPrevConstant = prevMethod === "Constant Imputation";
    const isNewConstant = value === "Constant Imputation";
    // If switching between Constant Imputation and other methods, reset features
    const shouldResetFeatures = isPrevConstant !== isNewConstant;
    const updated = numericalMissingValueMethods.map((m, i) =>
      i === idx
        ? {
            ...m,
            method: value,
            features: shouldResetFeatures ? [] : m.features,
          }
        : m
    );
    setNumericalMissingValueMethods(updated);
  };

  /**
   * Change categorical method for a specific index
   * @param {string} value - Method's name
   * @param {number} idx - index of method
   */
  const handleCategoricalOptionChange = (value, idx) => {
    const prevMethod = categoricalMissingValueMethods[idx]?.method;
    const isPrevConstant = prevMethod === "Constant Imputation";
    const isNewConstant = value === "Constant Imputation";
    // If switching between Constant Imputation and other methods, reset features
    const shouldResetFeatures = isPrevConstant !== isNewConstant;
    const updated = categoricalMissingValueMethods.map((m, i) =>
      i === idx
        ? {
            ...m,
            method: value,
            features: shouldResetFeatures ? [] : m.features,
          }
        : m
    );
    setCategoricalMissingValueMethods(updated);
  };

  /**
   * Change selected features checkbox for a specific method of numerical feature(except 'Constant Imputation' method)
   * @param {string} featureId - feature's id(eg. "RISK_MM-17")
   * @param {boolean} checked - feature checkbox are check or not. if checked, sent `true`
   * @param {number} idx - index of feature
   */
  const handleNumericalFeaturesChange = (featureId, checked, idx) => {
    const updated = numericalMissingValueMethods.map((m, i) => {
      if (i !== idx) return m;
      let newFeatures;
      if (checked) {
        newFeatures = [...m.features, featureId];
      } else {
        newFeatures = m.features.filter((id) => id !== featureId);
      }
      return { ...m, features: newFeatures };
    });
    setNumericalMissingValueMethods(updated);
  };

  /**
   * Change selected features checkbox for a specific method of categorical feature(except 'Constant Imputation' method)
   * @param {string} featureId - feature's id(eg. "RISK_MM-17")
   * @param {boolean} checked - feature checkbox are check or not. if checked, sent `true`
   * @param {number} idx - index of feature
   */
  const handleCategoricalFeaturesChange = (featureId, checked, idx) => {
    const updated = categoricalMissingValueMethods.map((m, i) => {
      if (i !== idx) return m;
      let newFeatures;
      if (checked) {
        newFeatures = [...m.features, featureId];
      } else {
        newFeatures = m.features.filter((id) => id !== featureId);
      }
      return { ...m, features: newFeatures };
    });
    setCategoricalMissingValueMethods(updated);
  };

  // Reset all checkboxes to uncheck
  const resetCheckboxes = () => {
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach((cb) => (cb.checked = false));
  };

  /**
   * Delete a specific numerical method
   * @param {number} idx - index of method
   */
  const handleDeleteNumericalMethod = (idx) => {
    const updated = numericalMissingValueMethods.filter((_, i) => i !== idx);
    setNumericalMissingValueMethods(updated);
  };

  /**
   * Delete a specific categorical method
   * @param {number} idx - index of method
   */
  const handleDeleteCategoricalMethod = (idx) => {
    const updated = categoricalMissingValueMethods.filter((_, i) => i !== idx);
    setCategoricalMissingValueMethods(updated);
  };

  /**
   * Toggle feature checkbox in 'Drop Rows' method
   * @param {string} featureId - feature's id(eg. "RISK_MM-17")
   * @param {boolean} checked - feature checkbox are check or not. if checked, sent `true`
   */
  const handleDropRowsFeaturesChange = (featureId, checked) => {
    let newDropRowFeatures;
    if (checked) {
      newDropRowFeatures = [...dropRowFeatures, featureId];
    } else {
      newDropRowFeatures = dropRowFeatures.filter(
        (feature) => feature !== featureId
      );
    }
    setDropRowFeatures(newDropRowFeatures);
  };

  /**
   * Toggle feature checkbox in 'Drop Columns' method
   * @param {string} featureId - feature's id(eg. "RISK_MM-17")
   * @param {boolean} checked - feature checkbox are check or not. if checked, sent `true`
   */
  const handleDropColumnFeaturesChange = (featureId, checked) => {
    let newDropColumnFeatures;
    if (checked) {
      newDropColumnFeatures = [...dropColumnFeatures, featureId];
    } else {
      newDropColumnFeatures = dropColumnFeatures.filter(
        (feature) => feature !== featureId
      );
    }
    setDropColumnFeatures(newDropColumnFeatures);
  };

  // Show table when apply button is clicked
  const handleApply = () => {
    setShowTable(true);
  };

  // Close the table and reset the parameters when reset button is clicked
  const handleResetParameters = () => {
    setSelectedMissingValueOption("");
    setDropRowFeatures([]);
    setDropColumnFeatures([]);
    setNumericalMissingValueMethods([]);
    setCategoricalMissingValueMethods([]);
    setShowDropRowsThreshold(false);
    setDropRowsThresholdValue(50);
    setShowDropColumnsThreshold(false);
    setDropColumnsThresholdValue(50);
    setShowTable(false);
  };

  /**
   * Render numerical content when select 'Means Imputation', 'Median Imputation' or 'Constant Imputation' method in 'Impute missing value' method
   * @param {string} method - Method's name
   * @param {number} idx - Method's index
   */
  const renderNumericalImputeOptions = (method, idx) => {
    switch (method) {
      case "Means Imputation":
        return (
          <div>
            <h1 className="font-bold mb-4 text-xl">Select features :</h1>
            <div className="flex flex-col">
              {allNumericalFeatureOrder
                // filter only numerical feature that not selected by other numerical method
                .filter(
                  (id) =>
                    allNumericalItemsMap[id] &&
                    !numericalMissingValueMethods.some((m, i) => {
                      if (i === idx) return false;
                      // Handle different data structures
                      if (m.method === "Constant Imputation") {
                        // For Constant Imputation: features is array of { feature: string, value: any }
                        return m.features.some((f) => f.feature === id);
                      } else {
                        // For other methods: features is array of strings
                        return m.features.includes(id);
                      }
                    })
                )
                // Render feature checkbox
                .map((id) => {
                  const item = allNumericalItemsMap[id];
                  const checked =
                    numericalMissingValueMethods[idx]?.features?.includes(id);
                  return (
                    <div key={item.id} className="flex mr-4">
                      <input
                        type="checkbox"
                        id={`${item.id}-method-${idx}`}
                        className="mr-2"
                        checked={checked}
                        onChange={(e) =>
                          handleNumericalFeaturesChange(
                            item.id,
                            e.target.checked,
                            idx
                          )
                        }
                      />
                      {item.feature}
                    </div>
                  );
                })}
            </div>
          </div>
        );
      case "Median Imputation":
        return (
          <div>
            <h1 className="font-bold mb-4 text-xl">Select features :</h1>
            <div className="flex flex-col">
              {allNumericalFeatureOrder
                // filter only numerical feature that not selected by other numerical method
                .filter(
                  (id) =>
                    allNumericalItemsMap[id] &&
                    !numericalMissingValueMethods.some((m, i) => {
                      if (i === idx) return false;
                      // Handle different data structures
                      if (m.method === "Constant Imputation") {
                        // For Constant Imputation: features is array of { feature: string, value: any }
                        return m.features.some((f) => f.feature === id);
                      } else {
                        // For other methods: features is array of strings
                        return m.features.includes(id);
                      }
                    })
                )
                // Render feature checkbox
                .map((id) => {
                  const item = allNumericalItemsMap[id];
                  const checked =
                    numericalMissingValueMethods[idx]?.features?.includes(id);
                  return (
                    <div key={item.id} className="flex mr-4">
                      <input
                        type="checkbox"
                        id={`${item.id}-method-${idx}`}
                        className="mr-2"
                        checked={checked}
                        onChange={(e) =>
                          handleNumericalFeaturesChange(
                            item.id,
                            e.target.checked,
                            idx
                          )
                        }
                      />
                      {item.feature}
                    </div>
                  );
                })}
            </div>
          </div>
        );
      case "Constant Imputation":
        return (
          <div>
            <ConstantImputation
              // filter only numerical feature that not selected by other numerical method
              features={features.numerical.items.filter(
                (item) =>
                  !numericalMissingValueMethods.some((m, i) => {
                    if (i === idx) return false;
                    // Handle different data structures
                    if (m.method === "Constant Imputation") {
                      // For Constant Imputation: features is array of { feature: string, value: any }
                      return m.features.some((f) => f.feature === item.id);
                    } else {
                      // For other methods: features is array of strings
                      return m.features.includes(item.id);
                    }
                  })
              )}
              placeholder="Enter number" // Placeholder in value input field
              methodIndex={idx} // Sent the index of 'Constant Imputation' method
              isNumerical={true} // Check if feature are numerical feature or not
              numericalMissingValueMethods={numericalMissingValueMethods} // List of numerical method of 'Impute missing value'
              setNumericalMissingValueMethods={setNumericalMissingValueMethods} // Function for edit 'numericalMissingValueMethods'
            />
          </div>
        );
      default:
        return;
    }
  };

  /**
   * Render categorical content when select 'Most Frequent Imputation' or 'Constant Imputation' method in 'Impute missing value' method
   * @param {string} method - Method's name
   * @param {number} idx - Method's index
   */
  const renderCategoricalImputeOptions = (method, idx) => {
    switch (method) {
      case "Most Frequent Imputation":
        return (
          <div>
            <h1 className="font-bold mb-4 text-xl">Select features :</h1>
            <div className="flex flex-col">
              {allCategoricalFeatureOrder
                // filter categorical feature that not selected in other categorical method
                .filter(
                  (id) =>
                    allCategoricalItemsMap[id] &&
                    !categoricalMissingValueMethods.some((m, i) => {
                      if (i === idx) return false;
                      // Handle different data structures
                      if (m.method === "Constant Imputation") {
                        // For Constant Imputation: features is array of { feature: string, value: any }
                        return m.features.some((f) => f.feature === id);
                      } else {
                        // For other methods: features is array of strings
                        return m.features.includes(id);
                      }
                    })
                )
                // Render feature checkbox
                .map((id) => {
                  const item = allCategoricalItemsMap[id];
                  const checked =
                    categoricalMissingValueMethods[idx]?.features?.includes(id);
                  return (
                    <div key={item.id} className="flex mr-4">
                      <input
                        type="checkbox"
                        id={`${item.id}-method-${idx}`}
                        className="mr-2"
                        checked={checked}
                        onChange={(e) =>
                          handleCategoricalFeaturesChange(
                            item.id,
                            e.target.checked,
                            idx
                          )
                        }
                      />
                      {item.feature}
                    </div>
                  );
                })}
            </div>
          </div>
        );
      case "Constant Imputation":
        return (
          <div>
            <ConstantImputation
              features={[
                ...features.nominal.items,
                ...features.ordinal.items,
              ].filter(
                // Filter only categorical feature that not selected by other categorical method
                (item) =>
                  !categoricalMissingValueMethods.some((m, i) => {
                    if (i === idx) return false;
                    // Handle different data structures
                    if (m.method === "Constant Imputation") {
                      // For Constant Imputation: features is array of { feature: string, value: any }
                      return m.features.some((f) => f.feature === item.id);
                    } else {
                      // For other methods: features is array of strings
                      return m.features.includes(item.id);
                    }
                  })
              )}
              placeholder="Enter text or number"
              methodIndex={idx}
              isNumerical={false}
              categoricalMissingValueMethods={categoricalMissingValueMethods} // List of categorical method of 'Impute missing value'
              setCategoricalMissingValueMethods={
                setCategoricalMissingValueMethods
              } // Function for edit 'categoricalMissingValueMethods'
            />
          </div>
        );
      default:
        return;
    }
  };

  // Render numerical content when select 'Impute missing value' method
  const renderNumericalMethods = () =>
    numericalMissingValueMethods.map(
      (thisDropdownMethod, thisDropdownIndex) => (
        <div key={thisDropdownIndex} className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <Dropdown
              options={numericalImputeOptions.filter(
                // filter numerical method that still not selected
                (option) =>
                  !numericalMissingValueMethods.some(
                    (otherDropdownMethod, otherDropdownIndex) =>
                      otherDropdownIndex !== thisDropdownIndex &&
                      otherDropdownMethod.method === option
                  )
              )}
              placeholder="Select method" // Dropdown's placeholder
              tell={`Numerical data ${thisDropdownIndex + 1}:`} // Show order of method
              searchPlaceholder="Search methods..." // Dropdown search's placeholder
              value={thisDropdownMethod.method} // Show selected method
              setValue={(value) =>
                handleNumericalOptionChange(value, thisDropdownIndex)
              } // Handle change method
            />
            {thisDropdownIndex >
              0 /* if method's order are second and above, add delete method button */ && (
              <button
                onClick={() => handleDeleteNumericalMethod(thisDropdownIndex)}
                className="ml-2 p-1.5 text-red-500 hover:text-red-700"
                title="Delete method"
              >
                <svg viewBox="0 0 24 24" className="w-6 h-6">
                  <path
                    d="M18 6L6 18M6 6l12 12"
                    stroke="currentColor"
                    strokeWidth="2"
                  />
                </svg>
              </button>
            )}
          </div>
          {thisDropdownMethod.method &&
            renderNumericalImputeOptions(
              thisDropdownMethod.method,
              thisDropdownIndex
            )}
        </div>
      )
    );

  // Render categorical content when select 'Impute missing value' method
  const renderCategoricalMethods = () =>
    categoricalMissingValueMethods.map(
      (thisDropdownMethod, thisDropdownIndex) => (
        <div key={thisDropdownIndex} className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <Dropdown
              options={CategoricalImputeOptions.filter(
                // filter categorical method that still not selected in other method
                (option) =>
                  !categoricalMissingValueMethods.some(
                    (otherDropdownMethod, otherDropDownIndex) =>
                      otherDropDownIndex !== thisDropdownIndex &&
                      otherDropdownMethod.method === option
                  )
              )}
              placeholder="Select method"
              tell={`Categorical data ${thisDropdownIndex + 1}:`}
              searchPlaceholder="Search methods..."
              value={thisDropdownMethod.method}
              setValue={(value) =>
                handleCategoricalOptionChange(value, thisDropdownIndex)
              }
            />
            {thisDropdownIndex > 0 && (
              <button
                onClick={() => handleDeleteCategoricalMethod(thisDropdownIndex)}
                className="ml-2 p-1.5 text-red-500 hover:text-red-700"
                title="Delete method"
              >
                <svg viewBox="0 0 24 24" className="w-6 h-6">
                  <path
                    d="M18 6L6 18M6 6l12 12"
                    stroke="currentColor"
                    strokeWidth="2"
                  />
                </svg>
              </button>
            )}
          </div>
          {thisDropdownMethod.method &&
            renderCategoricalImputeOptions(
              thisDropdownMethod.method,
              thisDropdownIndex
            )}
        </div>
      )
    );

  // Render Missing value handling according to "Drop Rows", "Drop Columns" and "Impute missing value" method
  const renderContent = () => {
    switch (selectedMissingValueOption) {
      case "Drop Rows":
        return (
          <div>
            <h1 className="font-bold mb-4 text-xl">Select features :</h1>
            <div className="flex flex-col">
              {/* Show feature checkbox */}
              {allFeatureOrder
                // Filter show only relavate feature
                .filter((id) => allItemsMap[id] && !allItemsMap[id].disabled)
                .map((id) => {
                  const item = allItemsMap[id];
                  const checked = dropRowFeatures.includes(id);
                  return (
                    <div key={item.id} className="flex mr-4">
                      <input
                        type="checkbox"
                        id={item.id}
                        className="mr-2"
                        checked={checked}
                        onChange={(e) =>
                          handleDropRowsFeaturesChange(
                            item.id,
                            e.target.checked
                          )
                        }
                      />
                      {item.feature}
                    </div>
                  );
                })}
            </div>

            {/* Checkbox for toggle show drop row threshold slider */}
            <div className="flex items-center mt-4">
              <input
                type="checkbox"
                id="threshold-checkbox"
                checked={showDropRowsThreshold}
                onChange={() =>
                  handleShowDropRowsThresholdChange(!showDropRowsThreshold)
                }
                className="mr-2"
              />
              <label htmlFor="threshold-checkbox" className="text-lg">
                Use drop row threshold (optional)
              </label>
            </div>

            {/* Show threshold slider if checkbox are checked */}
            {showDropRowsThreshold && (
              <CustomSlider
                text="Drop row threshold"
                unit=" %"
                defaultValue={dropRowsThresholdValue}
                min={0}
                max={100}
                step={1}
                onChange={handleDropRowsThresholdValueChange}
              />
            )}
          </div>
        );
      case "Drop Columns":
        return (
          <div>
            <h1 className="font-bold mb-4 text-xl">Select features :</h1>
            <div className="flex flex-col">
              {/* Filter only relavant feature */}
              {allFeatureOrder
                .filter((id) => allItemsMap[id] && !allItemsMap[id].disabled)
                .map((id) => {
                  const item = allItemsMap[id];
                  const checked = dropColumnFeatures.includes(id);
                  return (
                    <div key={item.id} className="flex mr-4">
                      <input
                        type="checkbox"
                        id={item.id}
                        className="mr-2"
                        checked={checked}
                        onChange={(e) =>
                          handleDropColumnFeaturesChange(
                            item.id,
                            e.target.checked
                          )
                        }
                      />
                      {item.feature}
                    </div>
                  );
                })}
            </div>

            {/* Checkbox for toggle show threshold slider */}
            <div className="flex items-center mt-4">
              <input
                type="checkbox"
                id="threshold-checkbox"
                checked={showDropColumnsThreshold}
                onChange={() =>
                  handleShowDropColumnsThresholdChange(
                    !showDropColumnsThreshold
                  )
                }
                className="mr-2"
              />
              <label htmlFor="threshold-checkbox" className="text-lg">
                Use Drop column threshold (optional)
              </label>
            </div>

            {/* Show threshold slider if checkbox are checked */}
            {showDropColumnsThreshold && (
              <CustomSlider
                text="Drop column threshold"
                unit=" %"
                defaultValue={dropColumnsThresholdValue}
                min={0}
                max={100}
                step={1}
                onChange={handleDropColumnsThresholdValueChange}
              />
            )}
          </div>
        );
      case "Impute missing value":
        return (
          <div className="flex">
            {/* Render Numerical method */}
            <div className="flex flex-col flex-1">
              {renderNumericalMethods()}

              {/* Button for add numerical method */}
              <button
                onClick={handleAddNumericalMethod}
                className={`${
                  numericalMissingValueMethods.length >= 3
                    ? "bg-blue-200"
                    : "bg-blue-500"
                } text-white px-4 py-2 rounded mt-2`}
                disabled={numericalMissingValueMethods.length >= 3}
              >
                Add method
              </button>
            </div>

            {/* Render categorical method */}
            <div className="flex flex-col flex-1">
              {renderCategoricalMethods()}

              {/* Button for add categorical method */}
              <button
                onClick={handleAddCategoricalMethod}
                className={`${
                  categoricalMissingValueMethods.length >= 2
                    ? "bg-blue-200"
                    : "bg-blue-500"
                } text-white px-4 py-2 rounded mt-2`}
                disabled={categoricalMissingValueMethods.length >= 2}
              >
                Add method
              </button>
            </div>
          </div>
        );
      default:
        return;
    }
  };

  // Update Redux state when local state changes
  useEffect(() => {
    // Reset checkboxes if 'selectedMissingValueOption' state is falsy value
    if (!selectedMissingValueOption) {
      resetCheckboxes();
    }
  }, [selectedMissingValueOption]);

  return (
    <div>
      {/* Dropdown for selecting the missing-value handling method */}
      <Dropdown
        options={options}
        placeholder="Select method"
        tell="Select missing-value handling method :"
        searchPlaceholder="Search methods..."
        value={selectedMissingValueOption}
        setValue={handleOptionChange}
      />

      {/* Render the content based on the selected option */}
      {renderContent()}

      {/* Button for applying the selected method and resetting the parameters */}
      <div className="flex flex-col items-center gap-5 p-5 w-full">
        <div className="flex gap-5">
          <button
            className="bg-[#a4c2f4] text-black py-4 px-6 text-lg rounded min-w-[250px] transition-colors hover:bg-[#8ab4f8] active:bg-[#7aa0e0]"
            onClick={handleApply}
          >
            Apply Missing Value Handling
          </button>
          <button
            className="bg-[#a4c2f4] text-black py-4 px-6 text-lg rounded min-w-[250px] transition-colors hover:bg-[#8ab4f8] active:bg-[#7aa0e0]"
            onClick={handleResetParameters}
          >
            Reset Parameters
          </button>
        </div>
      </div>

      {/* Show the table if showTable is true */}
      {showTable && (
        <ApplyTable
          originalData={dataset}
          cleanedData={dataset}
          tab="Missing Value"
        />
      )}
    </div>
  );
};

export default Missing_value;
