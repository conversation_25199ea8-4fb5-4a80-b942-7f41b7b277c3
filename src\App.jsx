import React from "react";
import {
  <PERSON><PERSON>erRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";

import DatasetSteps from "./pages/DatasetSteps";
import DatasetManagement from "./components/User/Dataset/Upload/DatasetManagement";
import DataCleansing from "./components/User/Dataset/Cleansing/DataCleansing";
import StatisticalAnalysis from "./components/User/Dataset/Statistic/StatisticalAnalysis";
import DataVisualization from "./components/User/Dataset/Visualization/DataVisualization";
import ModelPrediction from "./components/User/Dataset/Predict/ModelPrediction";

/**
 * The main application component for the machine learning data platform.
 * It sets up the router and defines all application routes.
 *
 * @component
 * @returns {JSX.Element} The main App component with routing and layout structure.
 */

const App = () => {
  return (
    <Router>
      <div className="flex flex-col h-screen">
        <div className="flex flex-1">
          <main className="flex-1 bg-gray-100 p-4 overflow-y-auto">
            <Routes>
              <>
                {/* Step-by-step dataset preparation page */}
                <Route path="/" element={<DatasetSteps />} />

                {/* Dataset upload and management page */}
                <Route path="/datasets" element={<DatasetManagement />} />

                {/* Data cleansing and preprocessing tools */}
                <Route path="/cleansing" element={<DataCleansing />} />

                {/* Data visualization interface */}
                <Route path="/visualization" element={<DataVisualization />} />

                {/* Statistical analysis and summary metrics */}
                <Route path="/statistics" element={<StatisticalAnalysis />} />

                {/* Machine learning prediction and evaluation */}
                <Route path="/ml" element={<ModelPrediction />} />
              </>

              {/* Fallback: redirect any unknown path to dashboard */}
              <Route path="*" element={<Navigate to="/" />} />
            </Routes>
          </main>
        </div>
      </div>
    </Router>
  );
};

export default App;
