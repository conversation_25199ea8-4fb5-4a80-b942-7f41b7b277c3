import React, { useState, useEffect, use } from "react";
import { setMissingValue } from "../../../../../app/features/progress/cleansing/missingValue";

/**
 * ConstantImputation component allows the user to input constant values to fill missing data.
 * This supports both numerical and categorical features.
 *
 * @component
 * @param {Object} props - Component props
 * @param {Array<{ id: string, feature: string }>} props.features - List of available features
 * @param {boolean} [props.isNumerical=true] - Whether the imputation is for numerical features
 * @param {string} [props.placeholder] - Placeholder text for value input
 * @param {number} props.methodIndex - Index of the current constant imputation method in Redux
 * @returns {JSX.Element} A rendered constant imputation input form
 */
export default function ConstantImputation({
  features, // features to display
  isNumerical = true, // whether to display numerical features or categorical features
  placeholder, // placeholder text for the input field
  methodIndex, // index of the method(only constant imputation method) to display
  numericalMissingValueMethods, // List of numerical method of 'Impute missing value'
  setNumericalMissingValueMethods, // Function for edit 'numericalMissingValueMethods'
  categoricalMissingValueMethods, // List of categorical method of 'Impute missing value'
  setCategoricalMissingValueMethods // Function for edit 'categoricalMissingValueMethods'
}) {


  /**
  * Local state for managing input rows (feature + value).
  * Each row has a unique id, selected feature id, and constant value.
  */
  const [featureRows, setFeatureRows] = useState([
    {
      id: "row-" + Date.now(),
      feature: "",
      value: "",
    },
  ]);

  // Update Redux state when features are selected
  useEffect(() => {
    if (methodIndex !== undefined) {
      const selectedFeatures = featureRows
        .filter((row) => row.feature)
        .map((row) => ({
          feature: row.feature.replace(/-\d+$/, ''),
          value: isNumerical ? Number(row.value) : row.value, // Ensure number if isNumerical
        }));
      // console.log("selectedFeatures", selectedFeatures);
      // Create deep copies to compare
      const currentFeatures = isNumerical
        ? [...(numericalMissingValueMethods[methodIndex]?.features || [])]
        : [...(categoricalMissingValueMethods[methodIndex]?.features || [])];

      const hasChanged =
        JSON.stringify(currentFeatures) !== JSON.stringify(selectedFeatures);

      if (hasChanged) {
        if (isNumerical) {
          const updatedMethods = numericalMissingValueMethods.map(
            (method, idx) =>
              idx === methodIndex
                ? { ...method, features: selectedFeatures }
                : method
          );
          setNumericalMissingValueMethods(updatedMethods);
        } else {
          const updatedMethods = categoricalMissingValueMethods.map(
            (method, idx) =>
              idx === methodIndex
                ? { ...method, features: selectedFeatures }
                : method
          );
          setCategoricalMissingValueMethods(updatedMethods);
        }
      }
    }
  }, [
    featureRows,
    setNumericalMissingValueMethods,
    setCategoricalMissingValueMethods,
    methodIndex,
    // Don't include numericalMissingValueMethods and categoricalMissingValueMethods in the dependency array
    // as they will cause infinite loops when they change
    isNumerical,
  ]);

  // Remove empty feature rows when there are no more features available
  useEffect(() => {
    const selectedFeatureIds = featureRows
      .filter((row) => row.feature)
      .map((row) => row.feature);
    
    const availableFeatures = features.filter(
      (f) => !selectedFeatureIds.includes(f.id)
    );
    
    // If there are no available features and there are empty rows, remove them
    if (availableFeatures.length === 0) {
      const hasEmptyRows = featureRows.some((row) => !row.feature);
      if (hasEmptyRows) {
        setFeatureRows((prev) => {
          const filteredRows = prev.filter((row) => row.feature);
          
          // Only update if we actually have rows to remove
          if (filteredRows.length === prev.length) {
            return prev; // No change needed
          }
          
        
          
          return filteredRows;
        });
      }
    }
  }, [features]); // Only depend on features, not featureRows



  /**
   * Handles feature selection from dropdown.
   * @param {string} id - Row id
   * @param {string} value - Selected feature id
   */
  const handleFeatureChange = (id, value) => { 
    setFeatureRows((prev) =>
      prev.map((row) =>
        row.id === id
          ? {
            ...row,
            feature: value,
            dropdownOpen: false, // Close dropdown after selection
            search: "", // Clear search
          }
          : row
      )
    );
  };

  /**
   * Handles value input change for a row.
   * @param {string} id - Row id
   * @param {string} value - Input value
   */
  const handleValueChange = (id, value) => {
    setFeatureRows((prev) =>
      prev.map((row) => (row.id === id ? { ...row, value } : row))
    );
  };

  

  /**
  * Removes a feature row by ID.
  * @param {string} id - Row id to remove
  */
  const handleRemoveRow = (id) => {
    setFeatureRows((prev) => prev.filter((row) => row.id !== id));
  };

  /**
  * Adds a new empty feature row.
  */
  const handleAddFeature = () => {
    // Add new row without validation
    setFeatureRows((prev) => [
      ...prev,
      {
        id: "row-" + Date.now(),
        feature: "",
        value: "",
      },
    ]);
  };

  // Check if there are any available features to select
  const hasAvailableFeatures = features.length > 0 && 
    features.some((f) => !featureRows.some((row) => row.feature === f.id));

  // Check if there are any empty rows (rows without selected features)
  const hasEmptyRows = featureRows.some((row) => !row.feature);

  useEffect(() => {
    console.log("featureRows:", featureRows);
    
  }, [featureRows]);

  return (
    <div className="max-w-lg p-5 font-sans">
      <h1 className="text-2xl font-bold mb-5">Constant value to fill:</h1>

      <div className="mb-5 space-y-4">
        {featureRows.map((row, index) => {
          // Get all selected feature ids except for the current row
          const selectedFeatureIds = featureRows
            .filter((r) => r.id !== row.id)
            .map((r) => r.feature);

          return (
            <div key={row.id} className="mb-3">
              <div className="flex items-center gap-2.5">
                <div className="relative min-w-[180px]">
                  <button
                    type="button"
                    onClick={() =>
                      setFeatureRows((prev) =>
                        prev.map((r) =>
                          r.id === row.id
                            ? { ...r, dropdownOpen: !r.dropdownOpen }
                            : { ...r, dropdownOpen: false }
                        )
                      )
                    }
                    className={`w-full py-2.5 px-4 pr-8 rounded-full border appearance-none text-base cursor-pointer flex justify-between items-center`}
                  >
                    <span>
                      {features.find((f) => f.id === row.feature)?.feature ||
                        "Select feature"}
                    </span>
                    <span className="text-xs">v</span>
                  </button>
                  {row.dropdownOpen && (
                    <div className="absolute z-10 mt-2 w-full bg-white rounded-lg shadow-xl border border-gray-200">
                     
                      {/* Search box */}
                      <input
                        type="text"
                        className="w-full px-3 py-2 text-sm border-b border-gray-100 focus:outline-none"
                        placeholder="Search feature..."
                        value={row.search || ""}
                        onChange={(e) =>
                          setFeatureRows((prev) =>
                            prev.map((r) =>
                              r.id === row.id
                                ? { ...r, search: e.target.value }
                                : r
                            )
                          )
                        }
                      />

                      {/* Show available options */}
                      <div className="max-h-48 overflow-y-auto">
                        {features
                          .filter(
                            (item) =>
                              !selectedFeatureIds.includes(item.id) &&
                              (!row.search ||
                                item.feature
                                  .toLowerCase()
                                  .includes(row.search.toLowerCase()))
                          )
                          .map((item) => (
                            <div
                              key={item.id}
                              className="cursor-pointer px-5 py-2 hover:bg-gray-100"
                              onClick={() => {
                                handleFeatureChange(row.id, item.id); // (id, value)
                              }}
                            >
                              {item.feature}
                            </div>
                          ))}

                          {/* if feature are not left and not found by search text */}
                        {features.filter(
                          (item) =>
                            !selectedFeatureIds.includes(item.id) &&
                            (!row.search ||
                              item.feature
                                .toLowerCase()
                                .includes(row.search.toLowerCase()))
                        ).length === 0 && (
                            <div className="px-5 py-2 text-gray-500 italic">
                              No results found
                            </div>
                          )}
                      </div>
                    </div>
                  )}
                </div>

                 {/* Input field for receipt value */}
                <input
                  type={isNumerical ? "number" : "text"}
                  value={row.value}
                  onChange={(e) => handleValueChange(row.id, e.target.value)}
                  className={`max-w-50 flex-1 py-2.5 px-4 rounded border text-base`}
                  placeholder={placeholder || "Enter constant value"}
                />

                {/* If this feature row is the second component and above, add remove button.  */}
                {index > 0 && (
                  <button
                    onClick={() => handleRemoveRow(row.id)}
                    className="p-1.5 flex items-center justify-center"
                    aria-label="Remove feature"
                  >
                    <svg viewBox="0 0 24 24" className="w-6 h-6">
                      <path
                        d="M18 6L6 18M6 6l12 12"
                        stroke="red"
                        strokeWidth="3"
                      />
                    </svg>
                  </button>
                )}
              </div>
            </div>
          );
        })}
      </div>

      <button
        onClick={handleAddFeature}
        disabled={!hasAvailableFeatures || hasEmptyRows}
        className={`bg-[#7ba7ff] text-black py-3 px-5 rounded-full text-base font-medium w-full max-w-[250px] mx-auto block transition-colors hover:bg-[#6a95f5] ${!hasAvailableFeatures || hasEmptyRows
          ? "opacity-50 cursor-not-allowed"
          : ""
          }`}
      > 
        Add feature
      </button>
    </div>
  );
}
