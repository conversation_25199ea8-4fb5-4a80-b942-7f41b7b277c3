import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  selectedMissingValueOption: "", // <-- "Drop Rows", "Drop Columns", "Impute missing value"
  dropRowFeatures:[], // <-- array of ["Temp3pm"], use for collect feature from "Drop Rows" options
  dropColumnFeatures:[], // <-- array of ["Temp3pm"], use for collect feature from "Drop Columns" options
  // array of { method: string, features: [] }
  // if method is "Constant Imputation", array of { method: string, features: [ { feature: "Temp3pm-18", value: 12 } ] }
  numericalMissingValueMethods: [],
  // array of { method: string, features: [] }
  // if method is "Constant Imputation", array of { method: string, features: [ { feature: "WindGustDir-5", value: "East" } ] }
  categoricalMissingValueMethods: [],
  showDropRowsThreshold: false, // <-- trigger to show drop rows threshold
  dropRowsThresholdValue: 50, // <-- Drop rows threshold value
  showDropColumnsThreshold: false, // <-- trigger to show drop columns threshold
  dropColumnsThresholdValue: 50, // <-- Drop columns threshold value
};




export const missingValueSlice = createSlice({
  name: "missingValue",
  initialState,
  reducers: {
    // change initial state value
    setMissingValue: (state, action) => {
      return { ...state, ...action.payload };
    },
    // reset initial state value to default
    resetMissingValue: () => {
      return initialState;
    },
  },
});

export const { setMissingValue, resetMissingValue } = missingValueSlice.actions;
export default missingValueSlice.reducer;
