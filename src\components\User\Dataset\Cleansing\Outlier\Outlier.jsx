import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import Dropdown from "../dropdown";
import CustomSlider from "../CustomSlider";
import ApplyButton from "../ApplyButton";
import ApplyTable from "../ApplyTable";

// Helper function to remove the "-number" suffix from a feature name
const cleanUniqueId = (str) => str.replace(/-\d+$/, "");

const Outlier = () => {
  const uploadFeatures = useSelector((state) => state.upload.features);
  const dataset = useSelector((state) => state.upload.dataset);

  const [features, setFeatures] = useState([]); // <-- array of selected feature for handle outlier method
  const [selectedOutlierOption, setSelectedOutlierOption] = useState(""); // <-- array of ["Trimming", "Capping", "Power Transformation"]
  const [selectedTrimmingOption, setSelectedTrimmingOption] = useState(""); // <-- array of ["Z-score", "IQR", "Percentile"]
  const [selectedCappingOption, setSelectedCappingOption] = useState(""); // <-- array of ["Z-score", "IQR", "Percentile"]
  const [zScore, setZScore] = useState(3); // <-- Threshold value of z-score slider
  const [iqr, setIQR] = useState(1.5); // <-- Threshold value of IQR slider
  const [percentile, setPercentile] = useState({
    // <-- Upper and Lower value of percentile
    lower: 1,
    upper: 99,
  });
  const [powerTransformation, setPowerTransformation] = useState({
    // <-- { transformation: "Box-Cox", lambda: 1 }
    transformation: "",
    lambda: 1,
  });
  const [showLampda, setShowLampda] = useState(false); // use for toggle show lampda option
  const [showTable, setShowTable] = useState(false);

  // Use only numerical features
  const numericalItems = uploadFeatures.numerical.items;
  //numericalItems = [{id: "numerical-0", feature: "feature_name", disabled: false, uniqueValue: [1, 2, 3]},
  //                  {id: "numerical-1", feature: "feature_name", disabled: false, uniqueValue: [1, 2, 3]}]
  const allNumericalItemsMap = {};
  //allNumericalItemsMap = {
  //  "numerical-0": {id: "numerical-0", feature: "feature_name", disabled: false, uniqueValue: [1, 2, 3]},
  //  "numerical-1": {id: "numerical-1", feature: "feature_name", disabled: false, uniqueValue: [1, 2, 3]}
  //}
  numericalItems.forEach((item) => {
    allNumericalItemsMap[item.id] = item;
  });

  const allNumericalFeatureOrder = numericalItems.map((item) => item.id);
  //allNumericalFeatureOrder = ["numerical-0", "numerical-1"]

  // Helper to check if all selected features are positive
  const allSelectedFeaturesPositive = () => {
    if (features.length === 0) return false;

    console.log("Checking features for positivity:", features);
    console.log("Available numericalItems:", numericalItems);

    return features.every((featureName) => {
      const item = numericalItems.find((f) => f.feature === featureName);
      console.log(`Looking for feature: ${featureName}`, item);

      if (!item || !item.uniqueValue) {
        console.log(`Feature ${featureName} not found or no uniqueValue`);
        return false;
      }

      console.log(`Feature ${featureName} uniqueValue:`, item.uniqueValue);
      const allPositive = item.uniqueValue.every((v) => {
        const isPositive = typeof v === "number" && v > 0;
        console.log(
          `Value ${v} (type: ${typeof v}) is positive: ${isPositive}`
        );
        return isPositive;
      });

      console.log(`Feature ${featureName} all positive: ${allPositive}`);
      return allPositive;
    });
  };

  // function to handle the change of the outlier option
  const handleOptionChange = (value) => {
    setSelectedOutlierOption(value);
  };

  // function to handle the change of the trimming option
  const handleTrimmingOptionChange = (value) => {
    setSelectedTrimmingOption(value);
  };

  // function to handle the change of the capping option
  const handleCappingOptionChange = (value) => {
    setSelectedCappingOption(value);
  };

  // function to handle the change of the z-score threshold
  const handleThresholdZscoreValueChange = (value) => {
    setZScore(value);
  };

  // function to handle the change of the iqr multiplier
  const handleThresholdIQRValueChange = (value) => {
    setIQR(value);
  };

  // function to handle the change of the percentile lower value
  const handlePercentileLowerValue = (value) => {
    setPercentile({ ...percentile, lower: value });
  };

  // function to handle the change of the percentile lower value
  const handlePercentileUpperValue = (value) => {
    setPercentile({ ...percentile, upper: value });
  };

  const handleLampdaChange = (value) => {
    setShowLampda(value);
  };

  const handleLampdaValueChange = (value) => {
    setPowerTransformation({ ...powerTransformation, lambda: value });
  };

  // function to handle checkbox change for feature selection
  const handleFeatureCheckboxChange = (featureName, isChecked) => {
    if (isChecked) {
      if (!features.includes(featureName)) {
        setFeatures([...features, featureName]);
      }
    } else {
      // Remove feature from the array
      setFeatures(features.filter((f) => f !== featureName));
    }
  };

  // Show table when apply button is clicked
  const handleApply = () => {
    setShowTable(true);
  };

  // Close the table and reset the parameters when reset button is clicked
  const handleResetParameters = () => {
    setFeatures([]);
    setSelectedOutlierOption("");
    setSelectedTrimmingOption("");
    setSelectedCappingOption("");
    setZScore(3);
    setIQR(1.5);
    setPercentile({ lower: 1, upper: 99 });
    setPowerTransformation({ transformation: "", lambda: 1 });
    setShowLampda(false);
    setShowTable(false);
  };

  // function to render the trimming content
  const renderTrimmingContent = () => {
    switch (selectedTrimmingOption) {
      case "Z-score":
        return (
          <div>
            <CustomSlider
              text="Z-Score Threshold"
              unit=""
              defaultValue={zScore}
              min={1}
              max={5}
              step={1}
              onChange={handleThresholdZscoreValueChange}
            />
          </div>
        );

      case "IQR":
        return (
          <div>
            <CustomSlider
              text="IQR Multiplier (k)"
              unit=""
              defaultValue={iqr}
              min={0}
              max={10}
              step={0.5}
              onChange={handleThresholdIQRValueChange}
            />
          </div>
        );
      case "Percentile":
        return (
          <div>
            <CustomSlider
              text="Lower Percentile(%) Threshold:"
              unit=""
              defaultValue={percentile.lower}
              min={0}
              max={50}
              step={1}
              onChange={handlePercentileLowerValue}
            />
            <CustomSlider
              text="Upper Percentile(%) Threshold:"
              unit=""
              defaultValue={percentile.upper}
              min={50}
              max={100}
              step={1}
              onChange={handlePercentileUpperValue}
            />
          </div>
        );

      default:
        return;
    }
  };

  // function to render the capping content
  const renderCappingContent = () => {
    switch (selectedCappingOption) {
      case "Z-score":
        return (
          <div>
            <CustomSlider
              text="Z-Score Threshold"
              unit=""
              defaultValue={zScore}
              min={1}
              max={5}
              step={1}
              onChange={handleThresholdZscoreValueChange}
            />
          </div>
        );

      case "IQR":
        return (
          <div>
            <CustomSlider
              text="IQR Multiplier (k)"
              unit=""
              defaultValue={iqr}
              min={0}
              max={10}
              step={0.5}
              onChange={handleThresholdIQRValueChange}
            />
          </div>
        );
      case "Percentile":
        return (
          <div>
            <CustomSlider
              text="Lower Percentile(%) Threshold:"
              unit=""
              defaultValue={percentile.lower}
              min={0}
              max={50}
              step={1}
              onChange={handlePercentileLowerValue}
            />
            <CustomSlider
              text="Upper Percentile(%) Threshold:"
              unit=""
              defaultValue={percentile.upper}
              min={50}
              max={100}
              step={1}
              onChange={handlePercentileUpperValue}
            />
          </div>
        );

      default:
        return;
    }
  };

  // function to render the content
  const renderContent = () => {
    switch (selectedOutlierOption) {
      case "Trimming":
        return (
          <div>
            <Dropdown
              options={["Z-score", "IQR", "Percentile"]} // trimming method
              placeholder="Select method" // placeholder
              tell="Select Trimming Method :" // tell
              searchPlaceholder="Search method..." // search placeholder
              value={selectedTrimmingOption} // selected trimming option
              setValue={handleTrimmingOptionChange} // handle trimming option change
            />
            {renderTrimmingContent()}
          </div>
        );
      case "Capping":
        return (
          <div>
            <Dropdown
              options={["Z-score", "IQR", "Percentile"]} // capping method
              placeholder="Select method" // placeholder
              tell="Select Capping Method :" // tell
              searchPlaceholder="Search method..." // search placeholder
              value={selectedCappingOption} // selected capping option
              setValue={handleCappingOptionChange} // handle capping option change
            />
            {renderCappingContent()}
          </div>
        );

      case "Power Transformation":
        const boxCoxDisabled = !allSelectedFeaturesPositive();
        console.log("Power Transformation - boxCoxDisabled:", boxCoxDisabled);
        console.log("Selected features:", features);
        return (
          <div>
            <h1 className="font-bold mb-4 text-xl">Select Transformation :</h1>
            <input
              type="radio"
              id="yeo-johnson"
              name="transformation"
              value="Yeo-Johnson"
              checked={powerTransformation.transformation === "Yeo-Johnson"}
              onChange={() =>
                setPowerTransformation({
                  ...powerTransformation,
                  transformation: "Yeo-Johnson",
                })
              }
            />
              <label htmlFor="yeo-johnson">Yeo-Johnson</label>
            <br />
            <input
              type="radio"
              id="box-cox"
              name="transformation"
              value="Box-Cox"
              checked={powerTransformation.transformation === "Box-Cox"}
              onChange={() =>
                setPowerTransformation({
                  ...powerTransformation,
                  transformation: "Box-Cox",
                })
              }
              disabled={boxCoxDisabled}
            />
              <label htmlFor="box-cox">Box-Cox</label>
            {boxCoxDisabled && (
              <div style={{ color: "red", fontSize: "0.9em" }}>
                Box-Cox is only available when all selected features have
                positive values.
              </div>
            )}
            <br />
            <div className="flex items-center mt-4">
              <input
                type="checkbox"
                id="threshold-checkbox"
                checked={showLampda}
                onChange={() => handleLampdaChange(!showLampda)}
                className="mr-2"
              />
              <label htmlFor="threshold-checkbox" className="text-lg">
                Use Lambda (optional)
              </label>
            </div>
            {showLampda && (
              <div>
                <CustomSlider
                  text="Lampda Threshold:"
                  unit=""
                  defaultValue={powerTransformation.lambda}
                  min={-5.0}
                  max={5.0}
                  step={0.1}
                  onChange={handleLampdaValueChange}
                />
              </div>
            )}
          </div>
        );

      default:
        return;
    }
  };

  useEffect(() => {
    if (!showTable) {
      setFeatures([]);
      setSelectedOutlierOption("");
      setSelectedTrimmingOption("");
      setSelectedCappingOption("");
      setZScore(3);
      setIQR(1.5);
      setPercentile({ lower: 1, upper: 99 });
      setPowerTransformation({ transformation: "", lambda: 1 });
      setShowLampda(false);
    }
  }, [showTable]);

  useEffect(() => {
    console.log("features:", features);
  }, [features]);

  return (
    <div>
      <h1 className="font-bold mb-4 text-xl">
        Select numerical features to process :
      </h1>
      <div className="flex flex-col">
        {allNumericalFeatureOrder
          .filter(
            // filter the numerical features that are not disabled
            (id) =>
              allNumericalItemsMap[id] && !allNumericalItemsMap[id].disabled
          )
          .map((id) => {
            const item = allNumericalItemsMap[id];
            return (
              <div key={item.id} className="flex mr-4">
                <input
                  type="checkbox"
                  id={item.id}
                  className="mr-2"
                  checked={features.includes(item.feature)}
                  onChange={(e) =>
                    handleFeatureCheckboxChange(item.feature, e.target.checked)
                  }
                />
                {item.feature}
              </div>
            );
          })}
      </div>

      {/* Dropdown for selecting the outlier handling method */}
      <Dropdown
        options={["Trimming", "Capping", "Power Transformation"]} // outlier handling method
        placeholder="Select method" // placeholder
        tell="Select Outlier Handling Method :" // tell
        searchPlaceholder="Search method..." // search placeholder
        value={selectedOutlierOption} // selected outlier handling option
        setValue={handleOptionChange} // handle outlier handling option change
      />

      {/* Render the content based on the selected option */}
      {renderContent()}

      {/* Button for applying the selected method and resetting the parameters */}
      <div className="flex flex-col items-center gap-5 p-5 w-full">
        <div className="flex gap-5">
          <button
            className="bg-[#a4c2f4] text-black py-4 px-6 text-lg rounded min-w-[250px] transition-colors hover:bg-[#8ab4f8] active:bg-[#7aa0e0]"
            onClick={handleApply}
          >
            Apply Outlier Handling
          </button>
          <button
            className="bg-[#a4c2f4] text-black py-4 px-6 text-lg rounded min-w-[250px] transition-colors hover:bg-[#8ab4f8] active:bg-[#7aa0e0]"
            onClick={handleResetParameters}
          >
            Reset Parameters
          </button>
        </div>
      </div>

      {/* Show the table if showTable is true */}
      {showTable && (
        <ApplyTable
          originalData={dataset}
          cleanedData={dataset}
          tab="Outlier"
        />
      )}
    </div>
  );
};

export default Outlier;
