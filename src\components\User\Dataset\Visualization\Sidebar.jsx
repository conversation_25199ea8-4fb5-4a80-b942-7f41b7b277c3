import React from "react";

/**
 * Chart configuration options available for selection in the sidebar.
 * Each option defines the chart type, label to display, and required axes.
 *
 * @constant {Array<Object>}
 * @property {string} value - The unique identifier of the chart type.
 * @property {string} label - The user-friendly name shown in the dropdown.
 * @property {string[]} axes - Required axes for that chart type (e.g., x, y, hue).
 */
const chartOptions = [
  { value: "column", label: "Column Chart", axes: ["x", "y"] },
  { value: "groupedColumn", label: "Grouped Columns", axes: ["x", "y", "hue"] },
  { value: "line", label: "Line Chart", axes: ["x", "y"] },
  { value: "multipleLine", label: "Multiple Line Chart", axes: ["x", "y", "hue"] },
  { value: "area", label: "Area Chart", axes: ["x", "y"] },
  { value: "scatter", label: "Scatter Plot", axes: ["x", "y", "hue"] },
  { value: "pie", label: "Pie Chart", axes: ["hue"] },
  { value: "multiplePies", label: "Multiple Pies", axes: ["x", "y", "hue"] },
];

/**
 * Sidebar component provides chart configuration controls.
 * 
 * Allows users to select chart type and dynamically displays appropriate axis selectors
 * (e.g., X, Y, or Hue) based on the selected chart type.
 *
 * @component
 * @param {Object} props - React props
 * @param {string[]} props.features - List of feature names from the dataset
 * @param {Object} props.selections - Current user selections: chartType, x, y, hue
 * @param {Function} props.onSelectionChange - Callback to update selections; receives (key, value)
 * 
 * @example
 * <Sidebar
 *   features={["Country", "GDP", "Population"]}
 *   selections={{ chartType: "line", x: "Year", y: "GDP", hue: "" }}
 *   onSelectionChange={(key, value) => { ... }}
 * />
 *
 * @returns {JSX.Element} The sidebar interface for configuring the chart
 */
const Sidebar = ({ features, selections, onSelectionChange }) => {
  /**
  * Finds the selected chart configuration based on current `chartType`.
  * @type {Object | undefined}
  */
  const selectedChart = chartOptions.find(opt => opt.value === selections.chartType);

  /**
  * Extracts which axes should be shown for the selected chart type.
  * @type {string[]}
  */
  const visibleAxes = selectedChart?.axes || [];

  return (
    <div className="w-64 bg-gray-100 p-6 rounded-lg shadow-md h-fit">
      <h3 className="text-xl font-semibold mb-4 text-gray-800">Chart Settings</h3>

      {/* Chart Type Selector */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">Chart Type</label>
        <select
          value={selections.chartType}
          onChange={(e) => onSelectionChange("chartType", e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">Select Chart</option>
          {chartOptions.map(({ value, label }) => (
            <option key={value} value={value}>{label}</option>
          ))}
        </select>
      </div>

      {/* Dynamic Axis Selectors */}
      {visibleAxes.map((axis) => (
        <div key={axis} className="mb-4">
          <label className="block text-sm font-medium text-gray-700 capitalize mb-1">{axis} Axis</label>
          <select
            value={selections[axis]}
            onChange={(e) => onSelectionChange(axis, e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select {axis}</option>
            {features.map((feature) => (
              <option key={feature} value={feature}>{feature}</option>
            ))}
          </select>
        </div>
      ))}
    </div>
  );
};

export default Sidebar;
