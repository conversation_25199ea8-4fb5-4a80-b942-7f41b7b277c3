import React from "react";

/**
 * Calculates basic statistics for a numerical column in a dataset.
 *
 * @param {Object[]} data - The dataset (array of objects).
 * @param {string} column - The column name to analyze.
 * @returns {Object|null} An object containing statistics (mean, median, mode, min, max, range, count, stdDev, variance, coefficientOfVariation, q1, q3, iqr, skewness, kurtosis, zScores, mad), or null if no numeric data.
 */

// Helper function to calculate basic statistics for numerical data
// รับ data เป็น array ของ object (เช่น dataset) และ column เป็นชื่อคอลัมน์ที่ต้องการวิเคราะห์
const calculateStatistics = (data, column) => {
  // ดึงค่าของ column ที่ต้องการจากแต่ละแถว และกรองเอาเฉพาะค่าที่เป็นตัวเลข
  const values = data
    .map((item) => item[column])
    .filter((value) => typeof value === "number");

  if (values.length === 0) return null;

  // คำนวณค่าเฉลี่ย (mean)
  const mean = values.reduce((acc, val) => acc + val, 0) / values.length;

  // คัดลอก values และเรียงลำดับจากน้อยไปมาก
  const sortedValues = values.slice().sort((a, b) => a - b);

  // คำนวณค่ากลาง (median)
  const median =
    sortedValues.length % 2 === 0
      ? (sortedValues[sortedValues.length / 2 - 1] +
        sortedValues[sortedValues.length / 2]) /
      2
      : sortedValues[Math.floor(sortedValues.length / 2)];

  // คำนวณค่าที่พบมากที่สุด (mode) โดยใช้ object map เพื่อนับความถี่
  const mode = values.reduce(
    (acc, val) => {
      acc.map[val] = (acc.map[val] || 0) + 1;
      if (acc.map[val] > acc.maxCount) {
        acc.maxCount = acc.map[val];
        acc.mode = val;
      }
      return acc;
    },
    { map: {}, maxCount: 0, mode: null }
  ).mode;

  // หาค่าน้อยสุด (min), มากสุด (max), ช่วง (range), และจำนวนข้อมูล (count)
  const min = Math.min(...values);
  const max = Math.max(...values);
  const range = max - min;
  const count = values.length;

  // คำนวณความแปรปรวน (variance) และส่วนเบี่ยงเบนมาตรฐาน (stdDev)
  const squaredDifferences = values.map((value) => Math.pow(value - mean, 2));
  const variance =
    squaredDifferences.reduce((acc, val) => acc + val, 0) / values.length;
  const stdDev = Math.sqrt(variance);

  // คำนวณสัดส่วนการแปรปรวนเทียบกับค่าเฉลี่ย (CV)
  const coefficientOfVariation = (stdDev / mean) * 100;

  // คำนวณควอร์ไทล์ที่ 1 และ 3 และช่วง IQR
  const q1 = sortedValues[Math.floor((values.length - 1) * 0.25)];
  const q3 = sortedValues[Math.floor((values.length - 1) * 0.75)];
  const iqr = q3 - q1;

  // คำนวณความเบ้ (Skewness) ถ้าหารด้วย 0 จะให้ค่า 0
  const skewness = (3 * (mean - median)) / stdDev || 0; // Handle division by zero

  // คำนวณความสูงของ distribution (Kurtosis) โดยลบ 3 ให้ได้ excess kurtosis
  const kurtosis =
    values.reduce((acc, val) => acc + Math.pow((val - mean) / stdDev, 4), 0) /
    values.length -
    3;

  // คำนวณค่า Z-score ของแต่ละข้อมูล
  const zScores = values.map((value) => (value - mean) / stdDev);

  // คำนวณ Mean Absolute Deviation (MAD)
  const mad =
    values
      .map((value) => Math.abs(value - median))
      .reduce((acc, val) => acc + val, 0) / values.length;

  return {
    mean,
    median,
    mode,
    min,
    max,
    range,
    count,
    stdDev,
    variance,
    coefficientOfVariation,
    q1,
    q3,
    iqr,
    skewness,
    kurtosis,
    zScores,
    mad,
  };
};

/**
 * Calculates statistics for a categorical column in a dataset.
 *
 * @param {Object[]} data - The dataset (array of objects).
 * @param {string} column - The column name to analyze.
 * @returns {Object|null} An object containing statistics (uniqueCount, frequencyDistribution, relativeFrequency, mostFrequentCategory, leastFrequentCategory, entropy, majorityClassPercentage, diversityIndex), or null if no data.
 */
// Helper function to calculate statistics for categorical data
// ดึงค่าจาก column และกรองค่าที่ไม่ว่าง
const calculateCategoricalStatistics = (data, column) => {
  const values = data.map((item) => item[column]).filter((value) => value);

  if (values.length === 0) return null;

  // นับจำนวนความถี่ของแต่ละ category
  const frequencyDistribution = values.reduce((acc, value) => {
    acc[value] = (acc[value] || 0) + 1;
    return acc;
  }, {});

  const total = values.length;
  const uniqueCount = Object.keys(frequencyDistribution).length;

  // สร้าง array ของสัดส่วนแบบร้อยละ
  const relativeFrequency = Object.entries(frequencyDistribution).map(
    ([key, count]) => ({
      category: key,
      count,
      proportion: ((count / total) * 100).toFixed(2) + "%",
    })
  );

  // หาค่าหมวดหมู่ที่พบมากและน้อยที่สุด
  const mostFrequentCategory = Object.entries(frequencyDistribution).reduce(
    (a, b) => (b[1] > a[1] ? b : a)
  )[0];

  const leastFrequentCategory = Object.entries(frequencyDistribution).reduce(
    (a, b) => (b[1] < a[1] ? b : a)
  )[0];

  // คำนวณ Entropy วัดความกระจายของข้อมูล
  const entropy = -Object.values(frequencyDistribution)
    .map((count) => count / total)
    .reduce((acc, p) => acc + p * Math.log2(p), 0)
    .toFixed(2);

  // หาสัดส่วนของ class ที่พบมากที่สุด และดัชนีความหลากหลาย
  const majorityClassPercentage = (
    (Math.max(...Object.values(frequencyDistribution)) / total) *
    100
  ).toFixed(2);

  const diversityIndex = (-Object.values(frequencyDistribution)
    .map((count) => count / total)
    .reduce((acc, p) => acc + p * Math.log(p), 0)).toFixed(2);

  return {
    uniqueCount,
    frequencyDistribution,
    relativeFrequency,
    mostFrequentCategory,
    leastFrequentCategory,
    entropy,
    majorityClassPercentage,
    diversityIndex,
  };
};

/**
 * StatisticsPanel component displays detailed statistics for a selected column in a dataset.
 *
 * - For numerical columns, shows mean, median, mode, min, max, range, std. deviation, variance, coefficient of variation, quartiles, IQR, skewness, kurtosis, etc.
 * - For categorical columns, shows unique count, most/least frequent category, entropy, majority class percentage, diversity index, and frequency distribution.
 *
 * @component
 * @param {Object} props
 * @param {Object[]} props.dataset - The dataset (array of objects).
 * @param {string} props.selectedColumn - The column name to analyze.
 * @returns {JSX.Element}
 *
 * @example
 * <StatisticsPanel dataset={data} selectedColumn="age" />
 */
// รับ prop เป็น dataset และชื่อ column ที่เลือก
const StatisticsPanel = ({ dataset, selectedColumn }) => {
  // เรียกใช้ฟังก์ชันคำนวณสถิติตามชนิดของข้อมูล
  const numericalStats = calculateStatistics(dataset, selectedColumn);
  const categoricalStats = calculateCategoricalStatistics(
    dataset,
    selectedColumn
  );

  return (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold">
        Statistics for "{selectedColumn}"
      </h3>

      {/* If numerical statistics are available */}
      {numericalStats ? (
        <div className="grid grid-cols-1 gap-4">
          <div className="bg-white shadow-md rounded-lg p-4">
            <h4 className="text-lg font-semibold">Mean</h4>
            <p>{numericalStats.mean.toFixed(2)}</p>
          </div>
          <div className="bg-white shadow-md rounded-lg p-4">
            <h4 className="text-lg font-semibold">Median</h4>
            <p>{numericalStats.median}</p>
          </div>
          <div className="bg-white shadow-md rounded-lg p-4">
            <h4 className="text-lg font-semibold">Mode</h4>
            <p>{numericalStats.mode}</p>
          </div>
          <div className="bg-white shadow-md rounded-lg p-4">
            <h4 className="text-lg font-semibold">Min</h4>
            <p>{numericalStats.min}</p>
          </div>
          <div className="bg-white shadow-md rounded-lg p-4">
            <h4 className="text-lg font-semibold">Max</h4>
            <p>{numericalStats.max}</p>
          </div>
          <div className="bg-white shadow-md rounded-lg p-4">
            <h4 className="text-lg font-semibold">Range</h4>
            <p>{numericalStats.range}</p>
          </div>
          <div className="bg-white shadow-md rounded-lg p-4">
            <h4 className="text-lg font-semibold">Standard Deviation</h4>
            <p>{numericalStats.stdDev.toFixed(2)}</p>
          </div>
          <div className="bg-white shadow-md rounded-lg p-4">
            <h4 className="text-lg font-semibold">Variance</h4>
            <p>{numericalStats.variance.toFixed(2)}</p>
          </div>
          <div className="bg-white shadow-md rounded-lg p-4">
            <h4 className="text-lg font-semibold">Coefficient of Variation</h4>
            <p>{numericalStats.coefficientOfVariation.toFixed(2)}%</p>
          </div>
          <div className="bg-white shadow-md rounded-lg p-4">
            <h4 className="text-lg font-semibold">Interquartile Range (IQR)</h4>
            <p>{numericalStats.iqr}</p>
          </div>
          <div className="bg-white shadow-md rounded-lg p-4">
            <h4 className="text-lg font-semibold">Skewness</h4>
            <p>{numericalStats.skewness.toFixed(2)}</p>
          </div>
          <div className="bg-white shadow-md rounded-lg p-4">
            <h4 className="text-lg font-semibold">Kurtosis</h4>
            <p>{numericalStats.kurtosis.toFixed(2)}</p>
          </div>
        </div>
      ) : categoricalStats ? (
        <div className="grid grid-cols-1 gap-4">
          <div className="bg-white shadow-md rounded-lg p-4">
            <h4 className="text-lg font-semibold">Unique Count</h4>
            <p>{categoricalStats.uniqueCount}</p>
          </div>
          <div className="bg-white shadow-md rounded-lg p-4">
            <h4 className="text-lg font-semibold">Most Frequent Category</h4>
            <p>{categoricalStats.mostFrequentCategory}</p>
          </div>
          <div className="bg-white shadow-md rounded-lg p-4">
            <h4 className="text-lg font-semibold">Least Frequent Category</h4>
            <p>{categoricalStats.leastFrequentCategory}</p>
          </div>
          <div className="bg-white shadow-md rounded-lg p-4">
            <h4 className="text-lg font-semibold">Entropy</h4>
            <p>{categoricalStats.entropy}</p>
          </div>
          <div className="bg-white shadow-md rounded-lg p-4">
            <h4 className="text-lg font-semibold">Majority Class Percentage</h4>
            <p>{categoricalStats.majorityClassPercentage}%</p>
          </div>
          <div className="bg-white shadow-md rounded-lg p-4">
            <h4 className="text-lg font-semibold">Diversity Index</h4>
            <p>{categoricalStats.diversityIndex}</p>
          </div>
          <div className="bg-white shadow-md rounded-lg p-4">
            <h4 className="text-lg font-semibold">Frequency Distribution</h4>
            <ul>
              {categoricalStats.relativeFrequency.map((item, index) => (
                <li key={index}>
                  {item.category}: {item.count} ({item.proportion})
                </li>
              ))}
            </ul>
          </div>
        </div>
      ) : (
        <p>No data available for the selected column.</p>
      )}
    </div>
  );
};

export default StatisticsPanel;
